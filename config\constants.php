    <?php

$defaultPath = public_path() . '/uploads/';
$viewPath = '/uploads/';

//$monthNameList = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

return [

    'perPage' => 20,

    'uploadFilePath' => [
        'companyDocument' => [
            'default' => public_path('uploads/companyprofile'), // ← Physical storage path
            'view' => '/uploads/companyprofile/' // ← Web-accessible path
        ]
    ],


    'occupationType' => [
        [ "id" => "Dr.",        "name" => "Dr."],
        [ "id" => "Hospital",   "name" => "Hospital"],
        [ "id" => "Clinic",     "name" => "Clinic"],
        [ "id" => "Dealer",     "name" => "Dealer"],
        [ "id" => "Pharmacy",   "name" => "Pharmacy"],
    ],

    'customerType' => [
        [ "id" => "Retail", "name" => "Retail"],
        [ "id" => "Tax",    "name" => "Tax"]
    ],

    'invoiceType' => [
        [ "id" => null,     "name" => "All TYPE"],
        [ "id" => "Retail", "name" => "Retail"],
        [ "id" => "Tax",    "name" => "Tax"]
    ],

    'paymentType' => [
        [ "id" => "cr", "name" => "Receipt"],
        [ "id" => "dr", "name" => "Payment"]
    ],

    'reportType' => [
        [ "id" => "Service Report",         "name" => "Service Report"],
        [ "id" => "Installation Report",    "name" => "Installation Report"],
        [ "id" => "Preventive Maintenance", "name" => "Preventive Maintenance"]
    ],

    'gstType' => [
        [ "id" => "IGST",       "name" => "IGST"],
        [ "id" => "CGST/SGST",  "name" => "CGST/SGST"]
    ],

    'productCategoryList' => [
        [ "id" => 'Sales',   "name" => "Sales"],
        [ "id" => 'Service', "name" => "Service"],
    ],

    'ordersStatus' => [
        [ "id" => null,         "name" => "All"],
        [ "id" => 'Pending',    "name" => "Pending"],
        [ "id" => 'In Process', "name" => "In Process"],
        [ "id" => 'Completed',  "name" => "Completed"],
        [ "id" => 'Cancelled',  "name" => "Cancelled"],
    ],

    'proformaStatus' => [
        [ "id" => null,         "name" => "All"],
        [ "id" => 'Open',    "name" => "Open"],
        [ "id" => 'Close', "name" => "Close"],
    ],

    'quotationList' => [
        [ "id" => null,      "name" => "All"],
        [ "id" => 'Sales',   "name" => "Sales"],
        [ "id" => 'Service', "name" => "Service"],
    ],

    'challanCategoryList' => [
        [ "id" => 'Sales',   "name" => "Sales"],
        [ "id" => 'Service', "name" => "Service"],
        [ "id" => 'Demo',    "name" => "Demo"],
    ],

    'leadSequenceStatus'  => [
        [ "id" => null,         "name" => "All"],
        [ "id" => 'pending',    "name" => "Pending"],
        [ "id" => 'completed',  "name" => "Completed"],
    ],

    'challanCategory' => [
        [ "id" => null,      "name" => "All"],
        [ "id" => 'Sales',   "name" => "Sales"],
        [ "id" => 'Service', "name" => "Service"],
        [ "id" => 'Demo',    "name" => "Demo"],
    ],

    'jobStatus' => [
        [ "id" => 'Repaired',        "name" => "Repaired"],
        [ "id" => 'Not Repaired',    "name" => "Not Repaired"],
        [ "id" => 'Return As It Is', "name" => "Return As It Is"],
    ],

    'jobFilterStatus' => [
        [ "id" => null,         "name" => "All"],
        [ "id" => 'Open',    "name" => "Open"],
        [ "id" => 'Close', "name" => "Close"],
    ],
    'maintenanceType' => [
        [ "id" => 'AMC',    "name" => "AMC"],
        [ "id" => 'CMC', "name" => "CMC"],
    ],

    'termsAndConditions' => [
        "validity"      => '30 days from the date of quotation',
        "delivery"      => 'Six to Eight weeks after valid PO',
        "payment_terms" => '50% advance and 50% against PI',
        "warranty"      => 'One year from the date of installation'
    ],

    'invoiceTermsAndConditions' => [
        "term1"      => 'Goods once sold will not be taken back or exchanged.',
        "term2"      => 'Bill not paid due date will attract 24% interest.',
        "term3" => 'All disputes subject to AHMEDABAD Jurisdication only.',
    ],

    'quotationBankinfo' => [
        "bank_name"     => 'Kotak Mahindra Bank',
        "branch_name"   => 'Sola Road, Ahmedabad',
        "account_no"    => '**********',
        "ifsc_code"     => 'KKBK0002576'
    ],

    'quotationHealthCareBankinfo' => [
        "bank_name"     => 'Kotak Mahindra Bank',
        "branch_name"   => 'Sola Road, Ahmedabad',
        "account_no"    => '**********',
        "ifsc_code"     => 'KKBK0002576'
    ],

    'quotationNoxBankinfo' => [
        "bank_name"     => 'HDFC Bank',
        "branch_name"   => 'Ghatlodiya, Ahmedabad',
        "account_no"    => '**************',
        "ifsc_code"     => 'HDFC0001337'
    ],

    'invoiceMCBankinfo' => [
        "bank_name"     => 'KOTAK MAHINDRA BANK',
        "branch_name"   => 'SOLA ROAD, AHMEDABAD',
        "account_no"    => '**********',
        "ifsc_code"     => 'KKBK0002576'
    ],

    'invoiceHCBankinfo' => [
        "bank_name"     => 'KOTAK MAHINDRA BANK',
        "branch_name"   => 'SATYASURYA COMPLEX,SATADHAR,SOLA ROAD.',
        "account_no"    => '**********',
        "ifsc_code"     => 'KKBK0002576'
    ],

    'invoiceNOXBankinfo' => [
        "bank_name"     => 'HDFC BANK',
        "branch_name"   => 'GHATLODIYA, AHMEDABAD.',
        "account_no"    => '**************',
        "ifsc_code"     => 'HDFC0001337'
    ],

    'isOrganization' => [
        [ "id" => 'yes', "name" => "Yes"],
        [ "id" => 'no',  "name" => "No"],
    ],

    'funnelStatus' => [
        [ "id" => 'Hot',  "name" => "Hot"],
        [ "id" => 'Warm', "name" => "Warm"],
        [ "id" => 'Cold', "name" => "Cold"]
    ],

    'inquiryTypes' => [
        [ "id" => null,     "name" => "All"],
        [ "id" => 'Hot',    "name" => "Hot"],
        [ "id" => 'Warm',   "name" => "Warm"],
        [ "id" => 'Cold',   "name" => "Cold"]
    ],

    //'months' => array_map(fn($month) => ['id' => $month, 'name' => $month], $monthNameList),
    'months' => [
        [ "id"=> "January",     "name"=> "January"],
        [ "id"=> "February",    "name"=> "February"],
        [ "id"=> "March",       "name"=> "March"],
        [ "id"=> "April",       "name"=> "April"],
        [ "id"=> "May",         "name"=> "May"],
        [ "id"=> "June",        "name"=> "June"],
        [ "id"=> "July",        "name"=> "July"],
        [ "id"=> "August",      "name"=> "August"],
        [ "id"=> "September",   "name"=> "September"],
        [ "id"=> "October",     "name"=> "October"],
        [ "id"=> "November",    "name"=> "November"],
        [ "id"=> "December",    "name"=> "December"]
    ],

    'paymentTypes' => [
        ['id' => 'cash', 'name' => 'Cash'],
        ['id' => 'check', 'name' => 'Cheque'],
        ['id' => 'NEFT', 'name' => 'NEFT']
    ],

    'pageTypes' => [
        ['id' => 'portrait', 'name' => 'Portrait'],
        ['id' => 'landscape', 'name' => 'Landscape']
    ]

];
