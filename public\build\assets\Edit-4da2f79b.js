import{r as p,o as l,c as i,a as o,u as s,w as c,F as k,Z as $,b as e,g as S,h as q,e as N,f as m,A as I,d as M,t as C}from"./app-d6eb42fc.js";import{_ as B,b as F}from"./AdminLayout-5cfec363.js";import{_ as x}from"./InputError-c242b5a3.js";import{_}from"./InputLabel-712384a1.js";import{P as V}from"./PrimaryButton-6e73d927.js";import{_ as b}from"./TextInput-8b0239ec.js";import{M as U}from"./Modal-30c61fff.js";import{u as D}from"./index-c6c459b3.js";import{Q as P}from"./vue-quill.snow-895114de.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const A={class:"animate-top max-w-3xl mx-auto"},L={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},Q={class:"border-b border-gray-200 pb-4 mb-6"},Z={class:"flex items-center gap-3"},z=e("h2",{class:"text-xl font-bold text-gray-900"},"Sequence Step Edit",-1),G={class:"ml-auto flex items-center justify-end gap-x-6"},H=["onSubmit"],J={class:"space-y-8"},K={class:"grid grid-cols-1 gap-6 sm:grid-cols-2"},O={class:"sm:col-span-1"},R={class:"sm:col-span-1"},W={class:"sm:grid-cols-1"},X={class:"sm:col-span-6"},Y={class:"flex mt-6 items-center justify-between"},ee={class:"ml-auto flex items-center justify-end gap-x-6"},te=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),se={key:0,class:"text-sm text-gray-600"},oe={class:"p-6 relative"},ae=e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1),ne=[ae],le=e("h2",{class:"text-lg font-medium text-gray-900"},"Email Tags",-1),ie={class:"mt-4 overflow-x-auto sm:rounded-lg"},re={class:"w-full text-sm text-left text-gray-500"},de=e("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50"},[e("tr",null,[e("th",{class:"px-4 py-2 text-gray-900"},"Tag Name"),e("th",{class:"px-4 py-2 text-gray-900"},"Description")])],-1),ce={key:0},ue=e("td",{colspan:"2",class:"px-4 py-4 text-center text-gray-500"}," No Email Tags Found. ",-1),pe=[ue],me={class:"divide-y divide-gray-300 bg-white"},_e={class:"px-4 py-2"},fe={class:"flex items-center space-x-2"},ve=["onClick"],ge={key:0,class:"text-green-600 text-xs"},ye={class:"px-4 py-2"},xe={class:"flex items-center space-x-2"},Ne={__name:"Edit",props:["sequence_id","nextStepNumber","stepInfo","tags"],setup(u){const r=u,t=D("post","/sequence-step",{id:r.stepInfo.id,sequence_id:r.sequence_id,step_number:r.stepInfo.step_number,days_after_previous_step:r.stepInfo.days_after_previous_step,subject:r.stepInfo.subject,content:r.stepInfo.content}),h=p(null),f=p(!1),j=d=>{h.value=d,f.value=!0},w=()=>{f.value=!1,h.value=null},v=p(null),g=p(""),T=(d,a,n)=>{navigator.clipboard.writeText(d).then(()=>{v.value=a,g.value=n,setTimeout(()=>{v.value=null,g.value=""},2e3)})},E=()=>{t.submit({preserveScroll:!0,onSuccess:()=>t.reset()})};return(d,a)=>(l(),i(k,null,[o(s($),{title:"Sequence Step Edit"}),o(B,null,{default:c(()=>[e("div",A,[e("div",L,[e("div",Q,[e("div",Z,[z,e("div",G,[o(V,{onClick:a[0]||(a[0]=n=>j(d.tag))},{default:c(()=>[S(" Email Tags ")]),_:1})])])]),e("form",{onSubmit:q(E,["prevent"])},[e("div",J,[e("div",K,[e("div",O,[o(_,{value:"Step Number"}),o(b,{id:"step_number",type:"number",modelValue:s(t).step_number,"onUpdate:modelValue":a[1]||(a[1]=n=>s(t).step_number=n),class:"w-full bg-gray-50",disabled:""},null,8,["modelValue"]),o(x,{message:s(t).errors.step_number,class:"mt-2"},null,8,["message"])]),e("div",R,[o(_,{for:"days_after_previous_step",value:"Day After Previous Step"}),o(b,{id:"days_after_previous_step",type:"text",modelValue:s(t).days_after_previous_step,"onUpdate:modelValue":a[2]||(a[2]=n=>s(t).days_after_previous_step=n),onChange:a[3]||(a[3]=n=>s(t).validate("days_after_previous_step"))},null,8,["modelValue"]),s(t).invalid("days_after_previous_step")?(l(),N(x,{key:0,message:s(t).errors.days_after_previous_step},null,8,["message"])):m("",!0)])]),e("div",W,[o(_,{value:"Email Subject"}),o(b,{id:"subject",type:"text",modelValue:s(t).subject,"onUpdate:modelValue":a[4]||(a[4]=n=>s(t).subject=n),class:"w-full",placeholder:"Enter email subject line",onChange:a[5]||(a[5]=n=>s(t).validate("subject"))},null,8,["modelValue"]),o(x,{message:s(t).errors.subject,class:"mt-2"},null,8,["message"])]),e("div",X,[o(_,{value:"Email Content"}),o(s(P),{content:s(t).content,"onUpdate:content":a[6]||(a[6]=n=>s(t).content=n),contentType:"html",theme:"snow",toolbar:"essential"},null,8,["content"])]),e("div",Y,[e("div",ee,[o(F,{href:d.route("sequence-step.show",{id:u.sequence_id})},{svg:c(()=>[te]),_:1},8,["href"]),o(V,{disabled:s(t).processing},{default:c(()=>[S("Update")]),_:1},8,["disabled"]),o(I,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:c(()=>[s(t).recentlySuccessful?(l(),i("p",se,"Saved.")):m("",!0)]),_:1})])])])],40,H)])]),o(U,{show:f.value,onClose:w},{default:c(()=>[e("div",oe,[(l(),i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6 text-gray-500 absolute top-4 right-4 cursor-pointer hover:text-red-500",onClick:w},ne)),le,e("div",ie,[e("table",re,[de,u.tags.length===0?(l(),i("tr",ce,pe)):m("",!0),e("tbody",me,[(l(!0),i(k,null,M(u.tags,(n,y)=>(l(),i("tr",{key:y},[e("td",_e,[e("div",fe,[e("span",null,C(n.name),1),e("span",{onClick:be=>T(n.name,y,"name"),class:"cursor-pointer"}," 📋 ",8,ve)]),v.value===y&&g.value==="name"?(l(),i("span",ge," Copied! ")):m("",!0)]),e("td",ye,[e("div",xe,[e("span",null,C(n.description),1)])])]))),128))])])])])]),_:1},8,["show"])]),_:1})],64))}};export{Ne as default};
