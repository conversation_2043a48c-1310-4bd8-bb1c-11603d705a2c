import{o,c as n,s as r}from"./app-039877b4.js";import{_ as s}from"./_plugin-vue_export-helper-c27b6911.js";const i=["type"],l={__name:"SecondaryButton",props:{type:{type:String,default:"button"}},setup(e){return(t,u)=>(o(),n("button",{type:e.type,class:"inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150"},[r(t.$slots,"default")],8,i))}},a={},c={class:"inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150"};function d(e,t){return o(),n("button",c,[r(e.$slots,"default")])}const g=s(a,[["render",d]]);export{g as D,l as _};
