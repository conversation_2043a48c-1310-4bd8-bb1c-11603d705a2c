import{T as d,o as i,e as c,w as l,a as s,u as t,Z as u,c as f,t as p,f as _,b as a,g,n as w,h as y}from"./app-d6eb42fc.js";import{_ as x}from"./GuestLayout-ce5699aa.js";import{_ as h}from"./InputError-c242b5a3.js";import{_ as b}from"./InputLabel-712384a1.js";import{P as k}from"./PrimaryButton-6e73d927.js";import{_ as V}from"./TextInput-8b0239ec.js";import"./_plugin-vue_export-helper-c27b6911.js";const v=a("h2",{class:"text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"}," Forgot your password ?",-1),B=a("div",{class:"mb-4 mt-2 text-sm text-gray-500"}," No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one. ",-1),N={key:0,class:"mb-4 font-medium text-sm text-green-600"},P=["onSubmit"],S={class:"flex items-center justify-end mt-4"},D={__name:"ForgotPassword",props:{status:{type:String}},setup(o){const e=d({email:""}),m=()=>{e.post(route("password.email"))};return($,r)=>(i(),c(x,null,{default:l(()=>[s(t(u),{title:"Forgot Password"}),v,B,o.status?(i(),f("div",N,p(o.status),1)):_("",!0),a("form",{onSubmit:y(m,["prevent"])},[a("div",null,[s(b,{for:"email",value:"Email"}),s(V,{id:"email",type:"email",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:t(e).email,"onUpdate:modelValue":r[0]||(r[0]=n=>t(e).email=n),required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),s(h,{class:"mt-2",message:t(e).errors.email},null,8,["message"])]),a("div",S,[s(k,{class:w({"opacity-25":t(e).processing}),disabled:t(e).processing},{default:l(()=>[g(" Email Password Reset Link ")]),_:1},8,["class","disabled"])])],40,P)]),_:1}))}};export{D as default};
