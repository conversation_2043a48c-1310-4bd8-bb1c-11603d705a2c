import{T as h,o as n,c as u,a as o,u as s,w as b,F as g,Z as U,b as t,h as $,k as m,v as _,d as f,y as v,g as w,n as S,t as y}from"./app-d6eb42fc.js";import{_ as q}from"./AdminLayout-5cfec363.js";import{_ as r}from"./InputError-c242b5a3.js";import{_ as d}from"./InputLabel-712384a1.js";import{P as A}from"./PrimaryButton-6e73d927.js";import{_ as C}from"./SecondaryButton-14965b81.js";import{_ as i}from"./TextInput-8b0239ec.js";import"./_plugin-vue_export-helper-c27b6911.js";const P=t("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"}," Add New Prospect ",-1),L={class:"py-12"},j={class:"max-w-4xl mx-auto sm:px-6 lg:px-8"},N={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},O={class:"p-6 text-gray-900"},B=["onSubmit"],R={class:"bg-gray-50 p-4 rounded-lg"},I=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Personal Information",-1),T={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},D={class:"bg-gray-50 p-4 rounded-lg"},F=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Company Information",-1),E={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},M={class:"bg-gray-50 p-4 rounded-lg"},W=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Lead Source & URLs",-1),z={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Z=["value"],G={class:"md:col-span-2"},H={class:"bg-gray-50 p-4 rounded-lg"},J=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Status & Priority",-1),K={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Q=["value"],X=["value"],Y={class:"bg-gray-50 p-4 rounded-lg"},ee=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Project & Budget Information",-1),se={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},le=t("option",{value:""},"Select budget range",-1),oe=["value"],te=t("option",{value:""},"Select user",-1),ae=["value"],re={class:"md:col-span-2"},de={class:"bg-gray-50 p-4 rounded-lg"},ie=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Conversation & Notes",-1),ne={class:"space-y-4"},ue={class:"flex items-center justify-end space-x-4"},ve={__name:"Add",props:{users:Array,statusOptions:Array,priorityOptions:Array,leadSourceOptions:Array,budgetRangeOptions:Array},setup(p){const e=h({first_name:"",last_name:"",email:"",phone:"",company:"",position:"",country:"",city:"",lead_source:"other",lead_source_details:"",linkedin_url:"",website_url:"",company_website:"",status:"new",priority:"medium",score:0,initial_conversation:"",notes:"",next_follow_up_at:"",estimated_budget:"",project_type:"",requirements:"",budget_range:"",assigned_to:""}),V=()=>{e.post(route("prospects.store"))},k=c=>c.charAt(0).toUpperCase()+c.slice(1).replace("_"," "),x=c=>({under_1k:"Under $1,000","1k_5k":"$1,000 - $5,000","5k_10k":"$5,000 - $10,000","10k_25k":"$10,000 - $25,000","25k_50k":"$25,000 - $50,000",over_50k:"Over $50,000"})[c]||c;return(c,a)=>(n(),u(g,null,[o(s(U),{title:"Add Prospect"}),o(q,null,{header:b(()=>[P]),default:b(()=>[t("div",L,[t("div",j,[t("div",N,[t("div",O,[t("form",{onSubmit:$(V,["prevent"]),class:"space-y-6"},[t("div",R,[I,t("div",T,[t("div",null,[o(d,{for:"first_name",value:"First Name *"}),o(i,{id:"first_name",modelValue:s(e).first_name,"onUpdate:modelValue":a[0]||(a[0]=l=>s(e).first_name=l),type:"text",class:"mt-1 block w-full",required:""},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.first_name},null,8,["message"])]),t("div",null,[o(d,{for:"last_name",value:"Last Name *"}),o(i,{id:"last_name",modelValue:s(e).last_name,"onUpdate:modelValue":a[1]||(a[1]=l=>s(e).last_name=l),type:"text",class:"mt-1 block w-full",required:""},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.last_name},null,8,["message"])]),t("div",null,[o(d,{for:"email",value:"Email *"}),o(i,{id:"email",modelValue:s(e).email,"onUpdate:modelValue":a[2]||(a[2]=l=>s(e).email=l),type:"email",class:"mt-1 block w-full",required:""},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.email},null,8,["message"])]),t("div",null,[o(d,{for:"phone",value:"Phone"}),o(i,{id:"phone",modelValue:s(e).phone,"onUpdate:modelValue":a[3]||(a[3]=l=>s(e).phone=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.phone},null,8,["message"])])])]),t("div",D,[F,t("div",E,[t("div",null,[o(d,{for:"company",value:"Company"}),o(i,{id:"company",modelValue:s(e).company,"onUpdate:modelValue":a[4]||(a[4]=l=>s(e).company=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.company},null,8,["message"])]),t("div",null,[o(d,{for:"position",value:"Position"}),o(i,{id:"position",modelValue:s(e).position,"onUpdate:modelValue":a[5]||(a[5]=l=>s(e).position=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.position},null,8,["message"])]),t("div",null,[o(d,{for:"country",value:"Country"}),o(i,{id:"country",modelValue:s(e).country,"onUpdate:modelValue":a[6]||(a[6]=l=>s(e).country=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.country},null,8,["message"])]),t("div",null,[o(d,{for:"city",value:"City"}),o(i,{id:"city",modelValue:s(e).city,"onUpdate:modelValue":a[7]||(a[7]=l=>s(e).city=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.city},null,8,["message"])])])]),t("div",M,[W,t("div",z,[t("div",null,[o(d,{for:"lead_source",value:"Lead Source *"}),m(t("select",{id:"lead_source","onUpdate:modelValue":a[8]||(a[8]=l=>s(e).lead_source=l),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",required:""},[(n(!0),u(g,null,f(p.leadSourceOptions,l=>(n(),u("option",{key:l,value:l},y(k(l)),9,Z))),128))],512),[[_,s(e).lead_source]]),o(r,{class:"mt-2",message:s(e).errors.lead_source},null,8,["message"])]),t("div",null,[o(d,{for:"lead_source_details",value:"Lead Source Details"}),o(i,{id:"lead_source_details",modelValue:s(e).lead_source_details,"onUpdate:modelValue":a[9]||(a[9]=l=>s(e).lead_source_details=l),type:"text",class:"mt-1 block w-full",placeholder:"Additional details about the source"},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.lead_source_details},null,8,["message"])]),t("div",null,[o(d,{for:"linkedin_url",value:"LinkedIn URL"}),o(i,{id:"linkedin_url",modelValue:s(e).linkedin_url,"onUpdate:modelValue":a[10]||(a[10]=l=>s(e).linkedin_url=l),type:"url",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.linkedin_url},null,8,["message"])]),t("div",null,[o(d,{for:"website_url",value:"Personal Website"}),o(i,{id:"website_url",modelValue:s(e).website_url,"onUpdate:modelValue":a[11]||(a[11]=l=>s(e).website_url=l),type:"url",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.website_url},null,8,["message"])]),t("div",G,[o(d,{for:"company_website",value:"Company Website"}),o(i,{id:"company_website",modelValue:s(e).company_website,"onUpdate:modelValue":a[12]||(a[12]=l=>s(e).company_website=l),type:"url",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.company_website},null,8,["message"])])])]),t("div",H,[J,t("div",K,[t("div",null,[o(d,{for:"status",value:"Status *"}),m(t("select",{id:"status","onUpdate:modelValue":a[13]||(a[13]=l=>s(e).status=l),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",required:""},[(n(!0),u(g,null,f(p.statusOptions,l=>(n(),u("option",{key:l,value:l},y(l.charAt(0).toUpperCase()+l.slice(1)),9,Q))),128))],512),[[_,s(e).status]]),o(r,{class:"mt-2",message:s(e).errors.status},null,8,["message"])]),t("div",null,[o(d,{for:"priority",value:"Priority *"}),m(t("select",{id:"priority","onUpdate:modelValue":a[14]||(a[14]=l=>s(e).priority=l),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",required:""},[(n(!0),u(g,null,f(p.priorityOptions,l=>(n(),u("option",{key:l,value:l},y(l.charAt(0).toUpperCase()+l.slice(1)),9,X))),128))],512),[[_,s(e).priority]]),o(r,{class:"mt-2",message:s(e).errors.priority},null,8,["message"])]),t("div",null,[o(d,{for:"score",value:"Score (0-100)"}),o(i,{id:"score",modelValue:s(e).score,"onUpdate:modelValue":a[15]||(a[15]=l=>s(e).score=l),type:"number",min:"0",max:"100",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.score},null,8,["message"])])])]),t("div",Y,[ee,t("div",se,[t("div",null,[o(d,{for:"project_type",value:"Project Type"}),o(i,{id:"project_type",modelValue:s(e).project_type,"onUpdate:modelValue":a[16]||(a[16]=l=>s(e).project_type=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.project_type},null,8,["message"])]),t("div",null,[o(d,{for:"estimated_budget",value:"Estimated Budget ($)"}),o(i,{id:"estimated_budget",modelValue:s(e).estimated_budget,"onUpdate:modelValue":a[17]||(a[17]=l=>s(e).estimated_budget=l),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.estimated_budget},null,8,["message"])]),t("div",null,[o(d,{for:"budget_range",value:"Budget Range"}),m(t("select",{id:"budget_range","onUpdate:modelValue":a[18]||(a[18]=l=>s(e).budget_range=l),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[le,(n(!0),u(g,null,f(p.budgetRangeOptions,l=>(n(),u("option",{key:l,value:l},y(x(l)),9,oe))),128))],512),[[_,s(e).budget_range]]),o(r,{class:"mt-2",message:s(e).errors.budget_range},null,8,["message"])]),t("div",null,[o(d,{for:"assigned_to",value:"Assign To"}),m(t("select",{id:"assigned_to","onUpdate:modelValue":a[19]||(a[19]=l=>s(e).assigned_to=l),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[te,(n(!0),u(g,null,f(p.users,l=>(n(),u("option",{key:l.id,value:l.id},y(l.name),9,ae))),128))],512),[[_,s(e).assigned_to]]),o(r,{class:"mt-2",message:s(e).errors.assigned_to},null,8,["message"])]),t("div",re,[o(d,{for:"requirements",value:"Requirements"}),m(t("textarea",{id:"requirements","onUpdate:modelValue":a[20]||(a[20]=l=>s(e).requirements=l),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Project requirements and details"},null,512),[[v,s(e).requirements]]),o(r,{class:"mt-2",message:s(e).errors.requirements},null,8,["message"])])])]),t("div",de,[ie,t("div",ne,[t("div",null,[o(d,{for:"initial_conversation",value:"Initial Conversation"}),m(t("textarea",{id:"initial_conversation","onUpdate:modelValue":a[21]||(a[21]=l=>s(e).initial_conversation=l),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Record of initial conversation"},null,512),[[v,s(e).initial_conversation]]),o(r,{class:"mt-2",message:s(e).errors.initial_conversation},null,8,["message"])]),t("div",null,[o(d,{for:"notes",value:"Notes"}),m(t("textarea",{id:"notes","onUpdate:modelValue":a[22]||(a[22]=l=>s(e).notes=l),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Additional notes about the prospect"},null,512),[[v,s(e).notes]]),o(r,{class:"mt-2",message:s(e).errors.notes},null,8,["message"])]),t("div",null,[o(d,{for:"next_follow_up_at",value:"Next Follow-up Date"}),o(i,{id:"next_follow_up_at",modelValue:s(e).next_follow_up_at,"onUpdate:modelValue":a[23]||(a[23]=l=>s(e).next_follow_up_at=l),type:"datetime-local",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{class:"mt-2",message:s(e).errors.next_follow_up_at},null,8,["message"])])])]),t("div",ue,[o(C,{href:c.route("prospects.index")},{default:b(()=>[w(" Cancel ")]),_:1},8,["href"]),o(A,{class:S({"opacity-25":s(e).processing}),disabled:s(e).processing},{default:b(()=>[w(" Create Prospect ")]),_:1},8,["class","disabled"])])],40,B)])])])])]),_:1})],64))}};export{ve as default};
