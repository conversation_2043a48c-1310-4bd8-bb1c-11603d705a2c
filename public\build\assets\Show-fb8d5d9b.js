import{r as U,T as k,o as a,c as i,a as n,u as d,w as r,F as S,Z,b as t,t as l,g as p,f as c,n as M,d as A,h as V,k as b,v as j,y as F}from"./app-d6eb42fc.js";import{_ as G,b as q}from"./AdminLayout-5cfec363.js";import{P as C}from"./PrimaryButton-6e73d927.js";import{_ as w}from"./SecondaryButton-14965b81.js";import{M as $}from"./Modal-30c61fff.js";import{_ as y}from"./InputLabel-712384a1.js";import{_ as N}from"./TextInput-8b0239ec.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const H={class:"flex justify-between items-center"},J={class:"font-semibold text-xl text-gray-800 leading-tight"},K={class:"flex space-x-2"},X=["disabled"],Y={class:"py-12"},tt={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},et={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},st={class:"p-6"},ot={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},lt={class:"space-y-4"},at=t("h3",{class:"text-lg font-medium text-gray-900"},"Basic Information",-1),it={class:"space-y-2"},nt=t("span",{class:"text-sm font-medium text-gray-500"},"Name:",-1),dt={class:"ml-2 text-sm text-gray-900"},ct=t("span",{class:"text-sm font-medium text-gray-500"},"Email:",-1),rt={class:"ml-2 text-sm text-gray-900"},ut={key:0},mt=t("span",{class:"text-sm font-medium text-gray-500"},"Phone:",-1),pt={class:"ml-2 text-sm text-gray-900"},yt={key:1},ft=t("span",{class:"text-sm font-medium text-gray-500"},"Company:",-1),xt={class:"ml-2 text-sm text-gray-900"},_t={key:2},vt=t("span",{class:"text-sm font-medium text-gray-500"},"Position:",-1),ht={class:"ml-2 text-sm text-gray-900"},gt={key:3},bt=t("span",{class:"text-sm font-medium text-gray-500"},"Location:",-1),wt={class:"ml-2 text-sm text-gray-900"},kt={class:"space-y-4"},St=t("h3",{class:"text-lg font-medium text-gray-900"},"Status & Priority",-1),Ct={class:"space-y-2"},Ut={class:"flex items-center"},At=t("span",{class:"text-sm font-medium text-gray-500"},"Status:",-1),Vt={class:"flex items-center"},Ft=t("span",{class:"text-sm font-medium text-gray-500"},"Priority:",-1),$t=t("span",{class:"text-sm font-medium text-gray-500"},"Score:",-1),Nt={class:"ml-2 text-sm text-gray-900"},Pt=t("span",{class:"text-sm font-medium text-gray-500"},"Lead Source:",-1),Lt={class:"ml-2 text-sm text-gray-900"},Mt={key:0},jt=t("span",{class:"text-sm font-medium text-gray-500"},"Assigned To:",-1),qt={class:"ml-2 text-sm text-gray-900"},Dt={class:"space-y-4"},Tt=t("h3",{class:"text-lg font-medium text-gray-900"},"Project Information",-1),Bt={class:"space-y-2"},It={key:0},Et=t("span",{class:"text-sm font-medium text-gray-500"},"Project Type:",-1),Ot={class:"ml-2 text-sm text-gray-900"},Rt={key:1},Wt=t("span",{class:"text-sm font-medium text-gray-500"},"Estimated Budget:",-1),zt={class:"ml-2 text-sm text-gray-900"},Qt={key:2},Zt=t("span",{class:"text-sm font-medium text-gray-500"},"Next Follow-up:",-1),Gt={class:"ml-2 text-sm text-gray-900"},Ht={key:3},Jt=t("span",{class:"text-sm font-medium text-gray-500"},"Converted Lead:",-1),Kt={key:0,class:"mt-6 pt-6 border-t border-gray-200"},Xt=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"URLs",-1),Yt={class:"flex flex-wrap gap-4"},te=["href"],ee=["href"],se=["href"],oe={key:1,class:"mt-6 pt-6 border-t border-gray-200"},le=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Notes & Conversation",-1),ae={class:"space-y-4"},ie={key:0},ne=t("h4",{class:"text-sm font-medium text-gray-700"},"Initial Conversation:",-1),de={class:"mt-1 text-sm text-gray-600"},ce={key:1},re=t("h4",{class:"text-sm font-medium text-gray-700"},"Requirements:",-1),ue={class:"mt-1 text-sm text-gray-600"},me={key:2},pe=t("h4",{class:"text-sm font-medium text-gray-700"},"Notes:",-1),ye={class:"mt-1 text-sm text-gray-600"},fe={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},xe={class:"p-6"},_e=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Quick Actions",-1),ve={class:"flex flex-wrap gap-3"},he={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},ge={class:"p-6"},be=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Activity Timeline",-1),we={key:0,class:"space-y-4"},ke={class:"flex-shrink-0"},Se={class:"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center"},Ce={class:"text-indigo-600 text-xs font-medium"},Ue={class:"flex-1 min-w-0"},Ae={class:"flex items-center justify-between"},Ve={class:"text-sm font-medium text-gray-900"},Fe={class:"text-xs text-gray-500"},$e={key:0,class:"mt-1 text-sm text-gray-600"},Ne={key:1,class:"mt-1 text-xs text-gray-500"},Pe={key:1,class:"text-center py-8 text-gray-500"},Le={class:"p-6"},Me=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Add Activity",-1),je=["onSubmit"],qe=["value"],De={class:"flex justify-end space-x-3"},Te={class:"p-6"},Be=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Update Status",-1),Ie=["onSubmit"],Ee=["value"],Oe={class:"flex justify-end space-x-3"},Re={class:"p-6"},We=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Schedule Follow-up",-1),ze=["onSubmit"],Qe={class:"flex justify-end space-x-3"},ss={__name:"Show",props:{prospect:Object},setup(e){const _=e,v=U(!1),h=U(!1),g=U(!1),m=k({activity_type:"note_added",title:"",description:"",activity_date:new Date().toISOString().slice(0,16)}),f=k({status:_.prospect.status,notes:""}),x=k({next_follow_up_at:"",notes:""}),P=k({}),D=()=>{m.post(route("prospects.addActivity",_.prospect.id),{onSuccess:()=>{v.value=!1,m.reset()}})},T=()=>{f.patch(route("prospects.updateStatus",_.prospect.id),{onSuccess:()=>{h.value=!1,f.reset()}})},B=()=>{x.post(route("prospects.scheduleFollowUp",_.prospect.id),{onSuccess:()=>{g.value=!1,x.reset()}})},I=()=>{P.post(route("prospects.convert",_.prospect.id))},E=u=>({new:"bg-blue-100 text-blue-800",contacted:"bg-yellow-100 text-yellow-800",qualified:"bg-green-100 text-green-800",unqualified:"bg-red-100 text-red-800",converted:"bg-purple-100 text-purple-800",lost:"bg-gray-100 text-gray-800"})[u]||"bg-gray-100 text-gray-800",O=u=>({low:"bg-gray-100 text-gray-800",medium:"bg-blue-100 text-blue-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[u]||"bg-gray-100 text-gray-800",L=u=>u?new Date(u).toLocaleString():"-",R=u=>u?new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(u):"-",W=u=>u.charAt(0).toUpperCase()+u.slice(1).replace("_"," "),z=[{value:"email_sent",label:"Email Sent"},{value:"email_received",label:"Email Received"},{value:"call_made",label:"Call Made"},{value:"call_received",label:"Call Received"},{value:"meeting_scheduled",label:"Meeting Scheduled"},{value:"meeting_completed",label:"Meeting Completed"},{value:"note_added",label:"Note Added"},{value:"linkedin_message",label:"LinkedIn Message"},{value:"proposal_sent",label:"Proposal Sent"},{value:"follow_up_scheduled",label:"Follow-up Scheduled"},{value:"document_shared",label:"Document Shared"},{value:"other",label:"Other"}],Q=["new","contacted","qualified","unqualified","converted","lost"];return(u,o)=>(a(),i(S,null,[n(d(Z),{title:`Prospect: ${e.prospect.first_name} ${e.prospect.last_name}`},null,8,["title"]),n(G,null,{header:r(()=>[t("div",H,[t("h2",J," Prospect: "+l(e.prospect.first_name)+" "+l(e.prospect.last_name),1),t("div",K,[n(q,{href:u.route("prospects.edit",e.prospect.id),class:"bg-indigo-600 text-white px-4 py-2 rounded-md"},{default:r(()=>[p(" Edit ")]),_:1},8,["href"]),e.prospect.status!=="converted"?(a(),i("button",{key:0,onClick:I,class:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700",disabled:d(P).processing}," Convert to Lead ",8,X)):c("",!0)])])]),default:r(()=>[t("div",Y,[t("div",tt,[t("div",et,[t("div",st,[t("div",ot,[t("div",lt,[at,t("div",it,[t("div",null,[nt,t("span",dt,l(e.prospect.first_name)+" "+l(e.prospect.last_name),1)]),t("div",null,[ct,t("span",rt,l(e.prospect.email),1)]),e.prospect.phone?(a(),i("div",ut,[mt,t("span",pt,l(e.prospect.phone),1)])):c("",!0),e.prospect.company?(a(),i("div",yt,[ft,t("span",xt,l(e.prospect.company),1)])):c("",!0),e.prospect.position?(a(),i("div",_t,[vt,t("span",ht,l(e.prospect.position),1)])):c("",!0),e.prospect.country||e.prospect.city?(a(),i("div",gt,[bt,t("span",wt,l(e.prospect.city)+l(e.prospect.city&&e.prospect.country?", ":"")+l(e.prospect.country),1)])):c("",!0)])]),t("div",kt,[St,t("div",Ct,[t("div",Ut,[At,t("span",{class:M([E(e.prospect.status),"ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},l(e.prospect.status.charAt(0).toUpperCase()+e.prospect.status.slice(1)),3)]),t("div",Vt,[Ft,t("span",{class:M([O(e.prospect.priority),"ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},l(e.prospect.priority.charAt(0).toUpperCase()+e.prospect.priority.slice(1)),3)]),t("div",null,[$t,t("span",Nt,l(e.prospect.score)+"/100",1)]),t("div",null,[Pt,t("span",Lt,l(W(e.prospect.lead_source)),1)]),e.prospect.assigned_user?(a(),i("div",Mt,[jt,t("span",qt,l(e.prospect.assigned_user.name),1)])):c("",!0)])]),t("div",Dt,[Tt,t("div",Bt,[e.prospect.project_type?(a(),i("div",It,[Et,t("span",Ot,l(e.prospect.project_type),1)])):c("",!0),e.prospect.estimated_budget?(a(),i("div",Rt,[Wt,t("span",zt,l(R(e.prospect.estimated_budget)),1)])):c("",!0),e.prospect.next_follow_up_at?(a(),i("div",Qt,[Zt,t("span",Gt,l(L(e.prospect.next_follow_up_at)),1)])):c("",!0),e.prospect.converted_lead?(a(),i("div",Ht,[Jt,n(q,{href:u.route("leads.show",e.prospect.converted_lead.id),class:"ml-2 text-sm text-indigo-600"},{default:r(()=>[p(" View Lead #"+l(e.prospect.converted_lead.id),1)]),_:1},8,["href"])])):c("",!0)])])]),e.prospect.linkedin_url||e.prospect.website_url||e.prospect.company_website?(a(),i("div",Kt,[Xt,t("div",Yt,[e.prospect.linkedin_url?(a(),i("a",{key:0,href:e.prospect.linkedin_url,target:"_blank",class:"text-blue-600 hover:text-blue-800"}," LinkedIn Profile ",8,te)):c("",!0),e.prospect.website_url?(a(),i("a",{key:1,href:e.prospect.website_url,target:"_blank",class:"text-blue-600 hover:text-blue-800"}," Personal Website ",8,ee)):c("",!0),e.prospect.company_website?(a(),i("a",{key:2,href:e.prospect.company_website,target:"_blank",class:"text-blue-600 hover:text-blue-800"}," Company Website ",8,se)):c("",!0)])])):c("",!0),e.prospect.initial_conversation||e.prospect.notes||e.prospect.requirements?(a(),i("div",oe,[le,t("div",ae,[e.prospect.initial_conversation?(a(),i("div",ie,[ne,t("p",de,l(e.prospect.initial_conversation),1)])):c("",!0),e.prospect.requirements?(a(),i("div",ce,[re,t("p",ue,l(e.prospect.requirements),1)])):c("",!0),e.prospect.notes?(a(),i("div",me,[pe,t("p",ye,l(e.prospect.notes),1)])):c("",!0)])])):c("",!0)])]),t("div",fe,[t("div",xe,[_e,t("div",ve,[n(C,{onClick:o[0]||(o[0]=s=>v.value=!0)},{default:r(()=>[p(" Add Activity ")]),_:1}),n(w,{onClick:o[1]||(o[1]=s=>h.value=!0)},{default:r(()=>[p(" Update Status ")]),_:1}),n(w,{onClick:o[2]||(o[2]=s=>g.value=!0)},{default:r(()=>[p(" Schedule Follow-up ")]),_:1})])])]),t("div",he,[t("div",ge,[be,e.prospect.activities&&e.prospect.activities.length>0?(a(),i("div",we,[(a(!0),i(S,null,A(e.prospect.activities,s=>(a(),i("div",{key:s.id,class:"flex items-start space-x-3 p-4 bg-gray-50 rounded-lg"},[t("div",ke,[t("div",Se,[t("span",Ce,l(s.activity_type.charAt(0).toUpperCase()),1)])]),t("div",Ue,[t("div",Ae,[t("p",Ve,l(s.title),1),t("p",Fe,l(L(s.activity_date)),1)]),s.description?(a(),i("p",$e,l(s.description),1)):c("",!0),s.user?(a(),i("p",Ne,"by "+l(s.user.name),1)):c("",!0)])]))),128))])):(a(),i("div",Pe," No activities recorded yet. "))])])])]),n($,{show:v.value,onClose:o[8]||(o[8]=s=>v.value=!1)},{default:r(()=>[t("div",Le,[Me,t("form",{onSubmit:V(D,["prevent"]),class:"space-y-4"},[t("div",null,[n(y,{for:"activity_type",value:"Activity Type"}),b(t("select",{id:"activity_type","onUpdate:modelValue":o[3]||(o[3]=s=>d(m).activity_type=s),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[(a(),i(S,null,A(z,s=>t("option",{key:s.value,value:s.value},l(s.label),9,qe)),64))],512),[[j,d(m).activity_type]])]),t("div",null,[n(y,{for:"activity_title",value:"Title"}),n(N,{id:"activity_title",modelValue:d(m).title,"onUpdate:modelValue":o[4]||(o[4]=s=>d(m).title=s),type:"text",class:"mt-1 block w-full",required:""},null,8,["modelValue"])]),t("div",null,[n(y,{for:"activity_description",value:"Description"}),b(t("textarea",{id:"activity_description","onUpdate:modelValue":o[5]||(o[5]=s=>d(m).description=s),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},null,512),[[F,d(m).description]])]),t("div",null,[n(y,{for:"activity_date",value:"Activity Date"}),n(N,{id:"activity_date",modelValue:d(m).activity_date,"onUpdate:modelValue":o[6]||(o[6]=s=>d(m).activity_date=s),type:"datetime-local",class:"mt-1 block w-full"},null,8,["modelValue"])]),t("div",De,[n(w,{onClick:o[7]||(o[7]=s=>v.value=!1)},{default:r(()=>[p("Cancel")]),_:1}),n(C,{disabled:d(m).processing},{default:r(()=>[p("Add Activity")]),_:1},8,["disabled"])])],40,je)])]),_:1},8,["show"]),n($,{show:h.value,onClose:o[12]||(o[12]=s=>h.value=!1)},{default:r(()=>[t("div",Te,[Be,t("form",{onSubmit:V(T,["prevent"]),class:"space-y-4"},[t("div",null,[n(y,{for:"status",value:"Status"}),b(t("select",{id:"status","onUpdate:modelValue":o[9]||(o[9]=s=>d(f).status=s),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[(a(),i(S,null,A(Q,s=>t("option",{key:s,value:s},l(s.charAt(0).toUpperCase()+s.slice(1)),9,Ee)),64))],512),[[j,d(f).status]])]),t("div",null,[n(y,{for:"status_notes",value:"Notes"}),b(t("textarea",{id:"status_notes","onUpdate:modelValue":o[10]||(o[10]=s=>d(f).notes=s),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Optional notes about the status change"},null,512),[[F,d(f).notes]])]),t("div",Oe,[n(w,{onClick:o[11]||(o[11]=s=>h.value=!1)},{default:r(()=>[p("Cancel")]),_:1}),n(C,{disabled:d(f).processing},{default:r(()=>[p("Update Status")]),_:1},8,["disabled"])])],40,Ie)])]),_:1},8,["show"]),n($,{show:g.value,onClose:o[16]||(o[16]=s=>g.value=!1)},{default:r(()=>[t("div",Re,[We,t("form",{onSubmit:V(B,["prevent"]),class:"space-y-4"},[t("div",null,[n(y,{for:"follow_up_date",value:"Follow-up Date"}),n(N,{id:"follow_up_date",modelValue:d(x).next_follow_up_at,"onUpdate:modelValue":o[13]||(o[13]=s=>d(x).next_follow_up_at=s),type:"datetime-local",class:"mt-1 block w-full",required:""},null,8,["modelValue"])]),t("div",null,[n(y,{for:"follow_up_notes",value:"Notes"}),b(t("textarea",{id:"follow_up_notes","onUpdate:modelValue":o[14]||(o[14]=s=>d(x).notes=s),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Notes about the follow-up"},null,512),[[F,d(x).notes]])]),t("div",Qe,[n(w,{onClick:o[15]||(o[15]=s=>g.value=!1)},{default:r(()=>[p("Cancel")]),_:1}),n(C,{disabled:d(x).processing},{default:r(()=>[p("Schedule Follow-up")]),_:1},8,["disabled"])])],40,ze)])]),_:1},8,["show"])]),_:1})],64))}};export{ss as default};
