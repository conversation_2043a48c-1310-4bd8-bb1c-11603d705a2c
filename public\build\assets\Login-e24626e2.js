import{T as _,i as x,o as d,e as h,w as i,a as t,u as e,Z as y,c as b,t as w,f as v,b as o,g as m,j as c,n as k,h as V}from"./app-039877b4.js";import{_ as B}from"./Checkbox-7fdc0d42.js";import{_ as S}from"./GuestLayout-fe2362bf.js";import{_ as u}from"./InputError-********.js";import{_ as p}from"./InputLabel-f7b6a8f4.js";import{P as N}from"./PrimaryButton-f92f9d42.js";import{_ as g}from"./TextInput-1323f3ba.js";import"./_plugin-vue_export-helper-c27b6911.js";const $=o("h2",{class:"text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"},"Sign in to your account",-1),P={key:0,class:"mb-4 font-medium text-sm text-green-600"},R=["onSubmit"],q={class:"mt-2"},C={class:"mt-2"},L={class:"mt-2"},U={class:"mb-4 flex justify-between items-center"},j={class:"flex items-center"},E=o("span",{class:"ml-2 text-center text-sm text-gray-500"},"Remember me",-1),F={class:"mt-2 flex items-center space-x-1"},M=o("span",{class:"text-sm text-gray-700"},"Not have an account ?",-1),J={__name:"Login",props:{canResetPassword:{type:Boolean},status:{type:String},canRegister:{type:Boolean}},setup(n){const s=_({email:"",password:"",remember:!1}),f=()=>{s.post(route("login"),{onFinish:()=>s.reset("password")})};return x(async()=>{localStorage.removeItem("permissions")}),(l,a)=>(d(),h(S,null,{default:i(()=>[t(e(y),{title:"Log in"}),$,n.status?(d(),b("div",P,w(n.status),1)):v("",!0),o("form",{onSubmit:V(f,["prevent"])},[o("div",q,[t(p,{for:"email",value:"Email"}),t(g,{id:"email",type:"email",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).email,"onUpdate:modelValue":a[0]||(a[0]=r=>e(s).email=r),required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),t(u,{class:"mt-2",message:e(s).errors.email},null,8,["message"])]),o("div",C,[t(p,{for:"password",value:"Password"}),t(g,{id:"password",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).password,"onUpdate:modelValue":a[1]||(a[1]=r=>e(s).password=r),required:"",autocomplete:"current-password"},null,8,["modelValue"]),t(u,{class:"mt-2",message:e(s).errors.password},null,8,["message"])]),o("div",L,[o("div",U,[o("label",j,[t(B,{name:"remember",checked:e(s).remember,"onUpdate:checked":a[2]||(a[2]=r=>e(s).remember=r)},null,8,["checked"]),E]),t(e(c),{href:l.route("password.request"),class:"font-semibold text-indigo-600 text-sm hover:text-indigo-500"},{default:i(()=>[m(" Forgot your password? ")]),_:1},8,["href"])])]),t(N,{class:k(["",{"opacity-25":e(s).processing}]),disabled:e(s).processing},{default:i(()=>[m(" Log in ")]),_:1},8,["class","disabled"]),o("div",F,[M,t(e(c),{href:l.route("register"),class:"font-semibold text-indigo-600 text-sm hover:text-indigo-500"},{default:i(()=>[m("Register ")]),_:1},8,["href"])])],40,R)]),_:1}))}};export{J as default};
