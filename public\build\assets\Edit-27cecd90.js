import{T as U,o as u,c as m,a as o,u as s,w as v,F as f,Z as $,b as t,t as _,h as q,k as p,v as y,d as b,y as w,g as V,n as S}from"./app-d6eb42fc.js";import{_ as P}from"./AdminLayout-5cfec363.js";import{_ as d}from"./InputError-c242b5a3.js";import{_ as i}from"./InputLabel-712384a1.js";import{P as C}from"./PrimaryButton-6e73d927.js";import{_ as j}from"./SecondaryButton-14965b81.js";import{_ as n}from"./TextInput-8b0239ec.js";import"./_plugin-vue_export-helper-c27b6911.js";const A={class:"font-semibold text-xl text-gray-800 leading-tight"},L={class:"py-12"},O={class:"max-w-4xl mx-auto sm:px-6 lg:px-8"},B={class:"bg-white overflow-hidden shadow-sm sm:rounded-lg"},N={class:"p-6 text-gray-900"},R=["onSubmit"],E={class:"bg-gray-50 p-4 rounded-lg"},I=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Personal Information",-1),T={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},D={class:"bg-gray-50 p-4 rounded-lg"},F=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Company Information",-1),M={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},W={class:"bg-gray-50 p-4 rounded-lg"},z=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Lead Source & URLs",-1),Z={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},G=["value"],H={class:"md:col-span-2"},J={class:"bg-gray-50 p-4 rounded-lg"},K=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Status & Priority",-1),Q={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},X=["value"],Y=["value"],ee={class:"bg-gray-50 p-4 rounded-lg"},se=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Project & Budget Information",-1),le={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},oe=t("option",{value:""},"Select budget range",-1),te=["value"],ae=t("option",{value:""},"Select user",-1),re=["value"],de={class:"md:col-span-2"},ie={class:"bg-gray-50 p-4 rounded-lg"},ne=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Conversation & Notes",-1),ue={class:"space-y-4"},me={class:"flex items-center justify-end space-x-4"},we={__name:"Edit",props:{prospect:Object,users:Array,statusOptions:Array,priorityOptions:Array,leadSourceOptions:Array,budgetRangeOptions:Array},setup(c){const r=c,e=U({first_name:r.prospect.first_name||"",last_name:r.prospect.last_name||"",email:r.prospect.email||"",phone:r.prospect.phone||"",company:r.prospect.company||"",position:r.prospect.position||"",country:r.prospect.country||"",city:r.prospect.city||"",lead_source:r.prospect.lead_source||"other",lead_source_details:r.prospect.lead_source_details||"",linkedin_url:r.prospect.linkedin_url||"",website_url:r.prospect.website_url||"",company_website:r.prospect.company_website||"",status:r.prospect.status||"new",priority:r.prospect.priority||"medium",score:r.prospect.score||0,initial_conversation:r.prospect.initial_conversation||"",notes:r.prospect.notes||"",next_follow_up_at:r.prospect.next_follow_up_at?r.prospect.next_follow_up_at.slice(0,16):"",estimated_budget:r.prospect.estimated_budget||"",project_type:r.prospect.project_type||"",requirements:r.prospect.requirements||"",budget_range:r.prospect.budget_range||"",assigned_to:r.prospect.assigned_to||""}),k=()=>{e.patch(route("prospects.update",r.prospect.id))},x=g=>g.charAt(0).toUpperCase()+g.slice(1).replace("_"," "),h=g=>({under_1k:"Under $1,000","1k_5k":"$1,000 - $5,000","5k_10k":"$5,000 - $10,000","10k_25k":"$10,000 - $25,000","25k_50k":"$25,000 - $50,000",over_50k:"Over $50,000"})[g]||g;return(g,a)=>(u(),m(f,null,[o(s($),{title:"Edit Prospect"}),o(P,null,{header:v(()=>[t("h2",A," Edit Prospect: "+_(c.prospect.first_name)+" "+_(c.prospect.last_name),1)]),default:v(()=>[t("div",L,[t("div",O,[t("div",B,[t("div",N,[t("form",{onSubmit:q(k,["prevent"]),class:"space-y-6"},[t("div",E,[I,t("div",T,[t("div",null,[o(i,{for:"first_name",value:"First Name *"}),o(n,{id:"first_name",modelValue:s(e).first_name,"onUpdate:modelValue":a[0]||(a[0]=l=>s(e).first_name=l),type:"text",class:"mt-1 block w-full",required:""},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.first_name},null,8,["message"])]),t("div",null,[o(i,{for:"last_name",value:"Last Name *"}),o(n,{id:"last_name",modelValue:s(e).last_name,"onUpdate:modelValue":a[1]||(a[1]=l=>s(e).last_name=l),type:"text",class:"mt-1 block w-full",required:""},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.last_name},null,8,["message"])]),t("div",null,[o(i,{for:"email",value:"Email *"}),o(n,{id:"email",modelValue:s(e).email,"onUpdate:modelValue":a[2]||(a[2]=l=>s(e).email=l),type:"email",class:"mt-1 block w-full",required:""},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.email},null,8,["message"])]),t("div",null,[o(i,{for:"phone",value:"Phone"}),o(n,{id:"phone",modelValue:s(e).phone,"onUpdate:modelValue":a[3]||(a[3]=l=>s(e).phone=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.phone},null,8,["message"])])])]),t("div",D,[F,t("div",M,[t("div",null,[o(i,{for:"company",value:"Company"}),o(n,{id:"company",modelValue:s(e).company,"onUpdate:modelValue":a[4]||(a[4]=l=>s(e).company=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.company},null,8,["message"])]),t("div",null,[o(i,{for:"position",value:"Position"}),o(n,{id:"position",modelValue:s(e).position,"onUpdate:modelValue":a[5]||(a[5]=l=>s(e).position=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.position},null,8,["message"])]),t("div",null,[o(i,{for:"country",value:"Country"}),o(n,{id:"country",modelValue:s(e).country,"onUpdate:modelValue":a[6]||(a[6]=l=>s(e).country=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.country},null,8,["message"])]),t("div",null,[o(i,{for:"city",value:"City"}),o(n,{id:"city",modelValue:s(e).city,"onUpdate:modelValue":a[7]||(a[7]=l=>s(e).city=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.city},null,8,["message"])])])]),t("div",W,[z,t("div",Z,[t("div",null,[o(i,{for:"lead_source",value:"Lead Source *"}),p(t("select",{id:"lead_source","onUpdate:modelValue":a[8]||(a[8]=l=>s(e).lead_source=l),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",required:""},[(u(!0),m(f,null,b(c.leadSourceOptions,l=>(u(),m("option",{key:l,value:l},_(x(l)),9,G))),128))],512),[[y,s(e).lead_source]]),o(d,{class:"mt-2",message:s(e).errors.lead_source},null,8,["message"])]),t("div",null,[o(i,{for:"lead_source_details",value:"Lead Source Details"}),o(n,{id:"lead_source_details",modelValue:s(e).lead_source_details,"onUpdate:modelValue":a[9]||(a[9]=l=>s(e).lead_source_details=l),type:"text",class:"mt-1 block w-full",placeholder:"Additional details about the source"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.lead_source_details},null,8,["message"])]),t("div",null,[o(i,{for:"linkedin_url",value:"LinkedIn URL"}),o(n,{id:"linkedin_url",modelValue:s(e).linkedin_url,"onUpdate:modelValue":a[10]||(a[10]=l=>s(e).linkedin_url=l),type:"url",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.linkedin_url},null,8,["message"])]),t("div",null,[o(i,{for:"website_url",value:"Personal Website"}),o(n,{id:"website_url",modelValue:s(e).website_url,"onUpdate:modelValue":a[11]||(a[11]=l=>s(e).website_url=l),type:"url",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.website_url},null,8,["message"])]),t("div",H,[o(i,{for:"company_website",value:"Company Website"}),o(n,{id:"company_website",modelValue:s(e).company_website,"onUpdate:modelValue":a[12]||(a[12]=l=>s(e).company_website=l),type:"url",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.company_website},null,8,["message"])])])]),t("div",J,[K,t("div",Q,[t("div",null,[o(i,{for:"status",value:"Status *"}),p(t("select",{id:"status","onUpdate:modelValue":a[13]||(a[13]=l=>s(e).status=l),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",required:""},[(u(!0),m(f,null,b(c.statusOptions,l=>(u(),m("option",{key:l,value:l},_(l.charAt(0).toUpperCase()+l.slice(1)),9,X))),128))],512),[[y,s(e).status]]),o(d,{class:"mt-2",message:s(e).errors.status},null,8,["message"])]),t("div",null,[o(i,{for:"priority",value:"Priority *"}),p(t("select",{id:"priority","onUpdate:modelValue":a[14]||(a[14]=l=>s(e).priority=l),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",required:""},[(u(!0),m(f,null,b(c.priorityOptions,l=>(u(),m("option",{key:l,value:l},_(l.charAt(0).toUpperCase()+l.slice(1)),9,Y))),128))],512),[[y,s(e).priority]]),o(d,{class:"mt-2",message:s(e).errors.priority},null,8,["message"])]),t("div",null,[o(i,{for:"score",value:"Score (0-100)"}),o(n,{id:"score",modelValue:s(e).score,"onUpdate:modelValue":a[15]||(a[15]=l=>s(e).score=l),type:"number",min:"0",max:"100",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.score},null,8,["message"])])])]),t("div",ee,[se,t("div",le,[t("div",null,[o(i,{for:"project_type",value:"Project Type"}),o(n,{id:"project_type",modelValue:s(e).project_type,"onUpdate:modelValue":a[16]||(a[16]=l=>s(e).project_type=l),type:"text",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.project_type},null,8,["message"])]),t("div",null,[o(i,{for:"estimated_budget",value:"Estimated Budget ($)"}),o(n,{id:"estimated_budget",modelValue:s(e).estimated_budget,"onUpdate:modelValue":a[17]||(a[17]=l=>s(e).estimated_budget=l),type:"number",step:"0.01",min:"0",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.estimated_budget},null,8,["message"])]),t("div",null,[o(i,{for:"budget_range",value:"Budget Range"}),p(t("select",{id:"budget_range","onUpdate:modelValue":a[18]||(a[18]=l=>s(e).budget_range=l),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[oe,(u(!0),m(f,null,b(c.budgetRangeOptions,l=>(u(),m("option",{key:l,value:l},_(h(l)),9,te))),128))],512),[[y,s(e).budget_range]]),o(d,{class:"mt-2",message:s(e).errors.budget_range},null,8,["message"])]),t("div",null,[o(i,{for:"assigned_to",value:"Assign To"}),p(t("select",{id:"assigned_to","onUpdate:modelValue":a[19]||(a[19]=l=>s(e).assigned_to=l),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[ae,(u(!0),m(f,null,b(c.users,l=>(u(),m("option",{key:l.id,value:l.id},_(l.name),9,re))),128))],512),[[y,s(e).assigned_to]]),o(d,{class:"mt-2",message:s(e).errors.assigned_to},null,8,["message"])]),t("div",de,[o(i,{for:"requirements",value:"Requirements"}),p(t("textarea",{id:"requirements","onUpdate:modelValue":a[20]||(a[20]=l=>s(e).requirements=l),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Project requirements and details"},null,512),[[w,s(e).requirements]]),o(d,{class:"mt-2",message:s(e).errors.requirements},null,8,["message"])])])]),t("div",ie,[ne,t("div",ue,[t("div",null,[o(i,{for:"initial_conversation",value:"Initial Conversation"}),p(t("textarea",{id:"initial_conversation","onUpdate:modelValue":a[21]||(a[21]=l=>s(e).initial_conversation=l),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Record of initial conversation"},null,512),[[w,s(e).initial_conversation]]),o(d,{class:"mt-2",message:s(e).errors.initial_conversation},null,8,["message"])]),t("div",null,[o(i,{for:"notes",value:"Notes"}),p(t("textarea",{id:"notes","onUpdate:modelValue":a[22]||(a[22]=l=>s(e).notes=l),rows:"3",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Additional notes about the prospect"},null,512),[[w,s(e).notes]]),o(d,{class:"mt-2",message:s(e).errors.notes},null,8,["message"])]),t("div",null,[o(i,{for:"next_follow_up_at",value:"Next Follow-up Date"}),o(n,{id:"next_follow_up_at",modelValue:s(e).next_follow_up_at,"onUpdate:modelValue":a[23]||(a[23]=l=>s(e).next_follow_up_at=l),type:"datetime-local",class:"mt-1 block w-full"},null,8,["modelValue"]),o(d,{class:"mt-2",message:s(e).errors.next_follow_up_at},null,8,["message"])])])]),t("div",me,[o(j,{href:g.route("prospects.index")},{default:v(()=>[V(" Cancel ")]),_:1},8,["href"]),o(C,{class:S({"opacity-25":s(e).processing}),disabled:s(e).processing},{default:v(()=>[V(" Update Prospect ")]),_:1},8,["class","disabled"])])],40,R)])])])])]),_:1})],64))}};export{we as default};
