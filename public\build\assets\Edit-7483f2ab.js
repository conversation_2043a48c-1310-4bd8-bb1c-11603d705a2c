import{_ as o}from"./AdminLayout-b2172af9.js";import i from"./DeleteUserForm-13a7a8c5.js";import m from"./UpdatePasswordForm-90909aef.js";import r from"./UpdateProfileInformationForm-0adebc79.js";import{o as l,c,a as t,u as n,w as e,F as d,Z as p,b as s}from"./app-039877b4.js";import"./DangerButton-5367ee25.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-71080952.js";import"./InputLabel-f7b6a8f4.js";import"./Modal-0f1198e2.js";/* empty css                                                              */import"./TextInput-1323f3ba.js";import"./PrimaryButton-f92f9d42.js";import"./TextArea-b1b590c7.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},q={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(d,null,[t(n(p),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{q as default};
