import{o as n,c as _,a,u as s,w as r,F as f,Z as g,b as t,h as v,e as m,f as l,g as b}from"./app-d6eb42fc.js";import{_ as h,b as x}from"./AdminLayout-5cfec363.js";import{_ as d}from"./InputError-c242b5a3.js";import{_ as c}from"./InputLabel-712384a1.js";import{P as y}from"./PrimaryButton-6e73d927.js";import{_ as V}from"./TextInput-8b0239ec.js";import{_ as $}from"./TextArea-d86dfd88.js";import{u as w}from"./index-c6c459b3.js";import"./_plugin-vue_export-helper-c27b6911.js";const C={class:"animate-top h-screen"},k={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},B=t("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create Tag",-1),N=["onSubmit"],S={class:"border-b border-gray-900/10 pb-12"},T={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},F={class:"sm:col-span-6"},j={class:"sm:col-span-6"},P={class:"flex mt-6 items-center justify-between"},U={class:"ml-auto flex items-center justify-end gap-x-6"},A=t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1),K={__name:"Add",setup(D){const e=w("post","/email-tag",{name:"",description:""}),p=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()});return(u,o)=>(n(),_(f,null,[a(s(g),{title:"Create Tag"}),a(h,null,{default:r(()=>[t("div",C,[t("div",k,[B,t("form",{onSubmit:v(p,["prevent"]),class:""},[t("div",S,[t("div",T,[t("div",F,[a(c,{for:"name",value:"Tag Name"}),a(V,{id:"name",type:"text",modelValue:s(e).name,"onUpdate:modelValue":o[0]||(o[0]=i=>s(e).name=i),autocomplete:"name",onChange:o[1]||(o[1]=i=>s(e).validate("name"))},null,8,["modelValue"]),s(e).invalid("name")?(n(),m(d,{key:0,class:"",message:s(e).errors.name},null,8,["message"])):l("",!0)]),t("div",j,[a(c,{for:"description",value:"Description"}),a($,{id:"description",type:"text",modelValue:s(e).description,"onUpdate:modelValue":o[2]||(o[2]=i=>s(e).description=i),rows:2,onChange:o[3]||(o[3]=i=>s(e).validate("description"))},null,8,["modelValue"]),s(e).invalid("description")?(n(),m(d,{key:0,class:"",message:s(e).errors.description},null,8,["message"])):l("",!0)])])]),t("div",P,[t("div",U,[a(x,{href:u.route("email-tag.index")},{svg:r(()=>[A]),_:1},8,["href"]),a(y,{disabled:s(e).processing},{default:r(()=>[b("Save")]),_:1},8,["disabled"])])])],40,N)])])]),_:1})],64))}};export{K as default};
