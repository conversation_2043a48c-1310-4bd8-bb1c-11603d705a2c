import{r,x,i as O,C as B,o as c,c as u,k as V,y as z,b as n,F as D,d as E,n as g,t as L,f as w}from"./app-039877b4.js";const S=n("svg",{class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[n("path",{"fill-rule":"evenodd",d:"M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z","clip-rule":"evenodd"})],-1),F=[S],N={key:0,class:"absolute z-10 mt-1 max-h-40 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm cursor-pointer",id:"options",role:"listbox","aria-labelledby":"combobox"},T=["onClick","onMouseenter"],I=n("svg",{class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[n("path",{"fill-rule":"evenodd",d:"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z","clip-rule":"evenodd"})],-1),U=[I],A={__name:"SearchableDropdownNew",props:["options","modelValue","editMode"],emits:["onchange"],setup(y,{emit:_}){const l=y,p=r(l.options),t=r(""),d=r(!1),i=r(-1),m=r(!1),v=()=>{const e=new RegExp(t.value,"i");p.value=l.options.filter(o=>e.test(o.name))},f=()=>{t.value="",v()},k=(e,o)=>{t.value=e,d.value=!1,m.value=!1,_("onchange",o,e)},h=()=>{l.editMode||(d.value=!0),m.value||f()},M=e=>{i.value=e};x(()=>l.options,()=>{v()}),x(()=>l.modelValue,e=>{e===""&&f()}),O(()=>{const e=l.options.find(o=>o.id===l.modelValue);e?(t.value=e.name,m.value=!1):f(),document.addEventListener("click",b)}),B(()=>{document.removeEventListener("click",b)});const b=e=>{e.target.closest(".relative")||(d.value=!1)};return(e,o)=>(c(),u("div",null,[V(n("input",{id:"combobox",type:"text",placeholder:"Search...",role:"combobox","onUpdate:modelValue":o[0]||(o[0]=s=>t.value=s),onInput:v,onFocus:h,autocomplete:"off",class:"w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-7 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,544),[[z,t.value]]),n("button",{type:"button",class:"absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none",onClick:h},F),d.value&&p.value.length?(c(),u("ul",N,[(c(!0),u(D,null,E(p.value,(s,a)=>(c(),u("li",{class:g(["relative cursor-default select-none py-2 pl-3 pr-9 cursor-pointer",{"text-white bg-indigo-600":i.value===a,"text-gray-900":i.value!==a}]),key:a,onClick:C=>k(s.name,s.id),onMouseenter:C=>M(a),tabindex:"-1",role:"option"},[n("span",{class:g(["block truncate",{"font-semibold":s.name===t.value}])},L(s.name),3),s.name===t.value?(c(),u("span",{key:0,class:g(["absolute inset-y-0 right-0 flex items-center pr-4",{"text-white":i.value===a,"text-indigo-600":i.value!==a}])},U,2)):w("",!0)],42,T))),128))])):w("",!0)]))}};export{A as _};
