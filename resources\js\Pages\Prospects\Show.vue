<script setup>
import { ref, defineProps } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import { Head, useForm } from '@inertiajs/vue3';

const props = defineProps({
  prospect: Object,
});

const showActivityModal = ref(false);
const showStatusModal = ref(false);
const showFollowUpModal = ref(false);

const activityForm = useForm({
  activity_type: 'note_added',
  title: '',
  description: '',
  activity_date: new Date().toISOString().slice(0, 16),
});

const statusForm = useForm({
  status: props.prospect.status,
  notes: '',
});

const followUpForm = useForm({
  next_follow_up_at: '',
  notes: '',
});

const convertForm = useForm({});

const addActivity = () => {
  activityForm.post(route('prospects.addActivity', props.prospect.id), {
    onSuccess: () => {
      showActivityModal.value = false;
      activityForm.reset();
    },
  });
};

const updateStatus = () => {
  statusForm.patch(route('prospects.updateStatus', props.prospect.id), {
    onSuccess: () => {
      showStatusModal.value = false;
      statusForm.reset();
    },
  });
};

const scheduleFollowUp = () => {
  followUpForm.post(route('prospects.scheduleFollowUp', props.prospect.id), {
    onSuccess: () => {
      showFollowUpModal.value = false;
      followUpForm.reset();
    },
  });
};

const convertToLead = () => {
  convertForm.post(route('prospects.convert', props.prospect.id));
};

const getStatusBadgeClass = (status) => {
  const classes = {
    'new': 'bg-blue-100 text-blue-800',
    'contacted': 'bg-yellow-100 text-yellow-800',
    'qualified': 'bg-green-100 text-green-800',
    'unqualified': 'bg-red-100 text-red-800',
    'converted': 'bg-purple-100 text-purple-800',
    'lost': 'bg-gray-100 text-gray-800',
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
};

const getPriorityBadgeClass = (priority) => {
  const classes = {
    'low': 'bg-gray-100 text-gray-800',
    'medium': 'bg-blue-100 text-blue-800',
    'high': 'bg-orange-100 text-orange-800',
    'urgent': 'bg-red-100 text-red-800',
  };
  return classes[priority] || 'bg-gray-100 text-gray-800';
};

const formatDate = (date) => {
  if (!date) return '-';
  return new Date(date).toLocaleString();
};

const formatCurrency = (amount) => {
  if (!amount) return '-';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const formatLeadSource = (source) => {
  return source.charAt(0).toUpperCase() + source.slice(1).replace('_', ' ');
};

const activityTypes = [
  { value: 'email_sent', label: 'Email Sent' },
  { value: 'email_received', label: 'Email Received' },
  { value: 'call_made', label: 'Call Made' },
  { value: 'call_received', label: 'Call Received' },
  { value: 'meeting_scheduled', label: 'Meeting Scheduled' },
  { value: 'meeting_completed', label: 'Meeting Completed' },
  { value: 'note_added', label: 'Note Added' },
  { value: 'linkedin_message', label: 'LinkedIn Message' },
  { value: 'proposal_sent', label: 'Proposal Sent' },
  { value: 'follow_up_scheduled', label: 'Follow-up Scheduled' },
  { value: 'document_shared', label: 'Document Shared' },
  { value: 'other', label: 'Other' },
];

const statusOptions = ['new', 'contacted', 'qualified', 'unqualified', 'converted', 'lost'];
</script>

<template>
  <Head :title="`Prospect: ${prospect.first_name} ${prospect.last_name}`" />

  <AdminLayout>
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
          Prospect: {{ prospect.first_name }} {{ prospect.last_name }}
        </h2>
        <div class="flex space-x-2">
          <ActionLink :href="route('prospects.edit', prospect.id)" class="bg-indigo-600 text-white px-4 py-2 rounded-md">
            Edit
          </ActionLink>
          <button
            v-if="prospect.status !== 'converted'"
            @click="convertToLead"
            class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
            :disabled="convertForm.processing"
          >
            Convert to Lead
          </button>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
        
        <!-- Prospect Overview -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              
              <!-- Basic Information -->
              <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                <div class="space-y-2">
                  <div>
                    <span class="text-sm font-medium text-gray-500">Name:</span>
                    <span class="ml-2 text-sm text-gray-900">{{ prospect.first_name }} {{ prospect.last_name }}</span>
                  </div>
                  <div>
                    <span class="text-sm font-medium text-gray-500">Email:</span>
                    <span class="ml-2 text-sm text-gray-900">{{ prospect.email }}</span>
                  </div>
                  <div v-if="prospect.phone">
                    <span class="text-sm font-medium text-gray-500">Phone:</span>
                    <span class="ml-2 text-sm text-gray-900">{{ prospect.phone }}</span>
                  </div>
                  <div v-if="prospect.company">
                    <span class="text-sm font-medium text-gray-500">Company:</span>
                    <span class="ml-2 text-sm text-gray-900">{{ prospect.company }}</span>
                  </div>
                  <div v-if="prospect.position">
                    <span class="text-sm font-medium text-gray-500">Position:</span>
                    <span class="ml-2 text-sm text-gray-900">{{ prospect.position }}</span>
                  </div>
                  <div v-if="prospect.country || prospect.city">
                    <span class="text-sm font-medium text-gray-500">Location:</span>
                    <span class="ml-2 text-sm text-gray-900">{{ prospect.city }}{{ prospect.city && prospect.country ? ', ' : '' }}{{ prospect.country }}</span>
                  </div>
                </div>
              </div>

              <!-- Status & Priority -->
              <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900">Status & Priority</h3>
                <div class="space-y-2">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-500">Status:</span>
                    <span :class="getStatusBadgeClass(prospect.status)" class="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                      {{ prospect.status.charAt(0).toUpperCase() + prospect.status.slice(1) }}
                    </span>
                  </div>
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-500">Priority:</span>
                    <span :class="getPriorityBadgeClass(prospect.priority)" class="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                      {{ prospect.priority.charAt(0).toUpperCase() + prospect.priority.slice(1) }}
                    </span>
                  </div>
                  <div>
                    <span class="text-sm font-medium text-gray-500">Score:</span>
                    <span class="ml-2 text-sm text-gray-900">{{ prospect.score }}/100</span>
                  </div>
                  <div>
                    <span class="text-sm font-medium text-gray-500">Lead Source:</span>
                    <span class="ml-2 text-sm text-gray-900">{{ formatLeadSource(prospect.lead_source) }}</span>
                  </div>
                  <div v-if="prospect.assigned_user">
                    <span class="text-sm font-medium text-gray-500">Assigned To:</span>
                    <span class="ml-2 text-sm text-gray-900">{{ prospect.assigned_user.name }}</span>
                  </div>
                </div>
              </div>

              <!-- Project Information -->
              <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900">Project Information</h3>
                <div class="space-y-2">
                  <div v-if="prospect.project_type">
                    <span class="text-sm font-medium text-gray-500">Project Type:</span>
                    <span class="ml-2 text-sm text-gray-900">{{ prospect.project_type }}</span>
                  </div>
                  <div v-if="prospect.estimated_budget">
                    <span class="text-sm font-medium text-gray-500">Estimated Budget:</span>
                    <span class="ml-2 text-sm text-gray-900">{{ formatCurrency(prospect.estimated_budget) }}</span>
                  </div>
                  <div v-if="prospect.next_follow_up_at">
                    <span class="text-sm font-medium text-gray-500">Next Follow-up:</span>
                    <span class="ml-2 text-sm text-gray-900">{{ formatDate(prospect.next_follow_up_at) }}</span>
                  </div>
                  <div v-if="prospect.converted_lead">
                    <span class="text-sm font-medium text-gray-500">Converted Lead:</span>
                    <ActionLink :href="route('leads.show', prospect.converted_lead.id)" class="ml-2 text-sm text-indigo-600">
                      View Lead #{{ prospect.converted_lead.id }}
                    </ActionLink>
                  </div>
                </div>
              </div>
            </div>

            <!-- URLs -->
            <div v-if="prospect.linkedin_url || prospect.website_url || prospect.company_website" class="mt-6 pt-6 border-t border-gray-200">
              <h3 class="text-lg font-medium text-gray-900 mb-4">URLs</h3>
              <div class="flex flex-wrap gap-4">
                <a v-if="prospect.linkedin_url" :href="prospect.linkedin_url" target="_blank" class="text-blue-600 hover:text-blue-800">
                  LinkedIn Profile
                </a>
                <a v-if="prospect.website_url" :href="prospect.website_url" target="_blank" class="text-blue-600 hover:text-blue-800">
                  Personal Website
                </a>
                <a v-if="prospect.company_website" :href="prospect.company_website" target="_blank" class="text-blue-600 hover:text-blue-800">
                  Company Website
                </a>
              </div>
            </div>

            <!-- Notes -->
            <div v-if="prospect.initial_conversation || prospect.notes || prospect.requirements" class="mt-6 pt-6 border-t border-gray-200">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Notes & Conversation</h3>
              <div class="space-y-4">
                <div v-if="prospect.initial_conversation">
                  <h4 class="text-sm font-medium text-gray-700">Initial Conversation:</h4>
                  <p class="mt-1 text-sm text-gray-600">{{ prospect.initial_conversation }}</p>
                </div>
                <div v-if="prospect.requirements">
                  <h4 class="text-sm font-medium text-gray-700">Requirements:</h4>
                  <p class="mt-1 text-sm text-gray-600">{{ prospect.requirements }}</p>
                </div>
                <div v-if="prospect.notes">
                  <h4 class="text-sm font-medium text-gray-700">Notes:</h4>
                  <p class="mt-1 text-sm text-gray-600">{{ prospect.notes }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div class="flex flex-wrap gap-3">
              <PrimaryButton @click="showActivityModal = true">
                Add Activity
              </PrimaryButton>
              <SecondaryButton @click="showStatusModal = true">
                Update Status
              </SecondaryButton>
              <SecondaryButton @click="showFollowUpModal = true">
                Schedule Follow-up
              </SecondaryButton>
            </div>
          </div>
        </div>

        <!-- Activities Timeline -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Activity Timeline</h3>
            <div v-if="prospect.activities && prospect.activities.length > 0" class="space-y-4">
              <div v-for="activity in prospect.activities" :key="activity.id" class="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                    <span class="text-indigo-600 text-xs font-medium">
                      {{ activity.activity_type.charAt(0).toUpperCase() }}
                    </span>
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <div class="flex items-center justify-between">
                    <p class="text-sm font-medium text-gray-900">{{ activity.title }}</p>
                    <p class="text-xs text-gray-500">{{ formatDate(activity.activity_date) }}</p>
                  </div>
                  <p v-if="activity.description" class="mt-1 text-sm text-gray-600">{{ activity.description }}</p>
                  <p v-if="activity.user" class="mt-1 text-xs text-gray-500">by {{ activity.user.name }}</p>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8 text-gray-500">
              No activities recorded yet.
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Activity Modal -->
    <Modal :show="showActivityModal" @close="showActivityModal = false">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Add Activity</h2>
        <form @submit.prevent="addActivity" class="space-y-4">
          <div>
            <InputLabel for="activity_type" value="Activity Type" />
            <select
              id="activity_type"
              v-model="activityForm.activity_type"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              <option v-for="type in activityTypes" :key="type.value" :value="type.value">
                {{ type.label }}
              </option>
            </select>
          </div>
          <div>
            <InputLabel for="activity_title" value="Title" />
            <TextInput
              id="activity_title"
              v-model="activityForm.title"
              type="text"
              class="mt-1 block w-full"
              required
            />
          </div>
          <div>
            <InputLabel for="activity_description" value="Description" />
            <textarea
              id="activity_description"
              v-model="activityForm.description"
              rows="3"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            ></textarea>
          </div>
          <div>
            <InputLabel for="activity_date" value="Activity Date" />
            <TextInput
              id="activity_date"
              v-model="activityForm.activity_date"
              type="datetime-local"
              class="mt-1 block w-full"
            />
          </div>
          <div class="flex justify-end space-x-3">
            <SecondaryButton @click="showActivityModal = false">Cancel</SecondaryButton>
            <PrimaryButton :disabled="activityForm.processing">Add Activity</PrimaryButton>
          </div>
        </form>
      </div>
    </Modal>

    <!-- Update Status Modal -->
    <Modal :show="showStatusModal" @close="showStatusModal = false">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Update Status</h2>
        <form @submit.prevent="updateStatus" class="space-y-4">
          <div>
            <InputLabel for="status" value="Status" />
            <select
              id="status"
              v-model="statusForm.status"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              <option v-for="status in statusOptions" :key="status" :value="status">
                {{ status.charAt(0).toUpperCase() + status.slice(1) }}
              </option>
            </select>
          </div>
          <div>
            <InputLabel for="status_notes" value="Notes" />
            <textarea
              id="status_notes"
              v-model="statusForm.notes"
              rows="3"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              placeholder="Optional notes about the status change"
            ></textarea>
          </div>
          <div class="flex justify-end space-x-3">
            <SecondaryButton @click="showStatusModal = false">Cancel</SecondaryButton>
            <PrimaryButton :disabled="statusForm.processing">Update Status</PrimaryButton>
          </div>
        </form>
      </div>
    </Modal>

    <!-- Schedule Follow-up Modal -->
    <Modal :show="showFollowUpModal" @close="showFollowUpModal = false">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Schedule Follow-up</h2>
        <form @submit.prevent="scheduleFollowUp" class="space-y-4">
          <div>
            <InputLabel for="follow_up_date" value="Follow-up Date" />
            <TextInput
              id="follow_up_date"
              v-model="followUpForm.next_follow_up_at"
              type="datetime-local"
              class="mt-1 block w-full"
              required
            />
          </div>
          <div>
            <InputLabel for="follow_up_notes" value="Notes" />
            <textarea
              id="follow_up_notes"
              v-model="followUpForm.notes"
              rows="3"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              placeholder="Notes about the follow-up"
            ></textarea>
          </div>
          <div class="flex justify-end space-x-3">
            <SecondaryButton @click="showFollowUpModal = false">Cancel</SecondaryButton>
            <PrimaryButton :disabled="followUpForm.processing">Schedule Follow-up</PrimaryButton>
          </div>
        </form>
      </div>
    </Modal>
  </AdminLayout>
</template>
