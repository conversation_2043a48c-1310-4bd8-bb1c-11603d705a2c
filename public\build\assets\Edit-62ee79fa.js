import{_ as o}from"./AdminLayout-5cfec363.js";import i from"./DeleteUserForm-7f6c6048.js";import m from"./UpdatePasswordForm-c5e0914b.js";import r from"./UpdateProfileInformationForm-6a5c8331.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-d6eb42fc.js";import"./DangerButton-293f02ed.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-c242b5a3.js";import"./InputLabel-712384a1.js";import"./Modal-30c61fff.js";/* empty css                                                              */import"./SecondaryButton-14965b81.js";import"./TextInput-8b0239ec.js";import"./PrimaryButton-6e73d927.js";import"./TextArea-d86dfd88.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
