<script setup>
import AdminLayout from "@/Layouts/AdminLayout.vue";
import { Head } from '@inertiajs/vue3';
import LineChart from '@/Components/LineChart.vue';
import PieChart from '@/Components/PieChart.vue';
import DashboardFilter from '@/Components/DashboardFilter.vue'
import Doughnut<PERSON>hart from '@/Components/DoughnutChart.vue';
import InputLabel from '@/Components/InputLabel.vue';
import { router, useForm } from '@inertiajs/vue3';
import { ref, watch, onMounted } from 'vue';



const props = defineProps(['deliveredEmails','leadCount', 'leadSequenceCount', 'sequenceCount', 'emailChart', 'filters', 'metrics']);

const searchValue = ref('');
const selectedRange = ref(props.filters?.range || 'custom');  // default to 'custom' if range is not present
const startDate = ref(props.filters?.startDate || '');  // default to empty if no start date
const endDate = ref(props.filters?.endDate || '');  // default to empty if no end date

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
    },
  },
  scales: {
    y: {
      beginAtZero: true,
      grid: {
        color: '#e5e7eb' // Tailwind gray-200
      },
    },
    x: {
      grid: {
        display: false
      }
    }
  },
  elements: {
    point: {
      radius: 4,
      backgroundColor: '#fff',
      borderWidth: 2
    },
    line: {
      tension: 0.3,
      borderWidth: 2,
    }
  }
};


onMounted(() => {
  if (!props.filters?.range) {
    selectedRange.value = 'custom';
    startDate.value = '';
    endDate.value = '';
  }
});

watch([selectedRange, startDate, endDate], () => {
  router.get(route('dashboard'), {
    range: selectedRange.value,
    startDate: startDate.value,
    endDate: endDate.value,
  }, {
    preserveScroll: true,
    preserveState: true,
    replace: true,
  });
});


</script>

<template>
    <Head title="Dashboard" />

    <AdminLayout>
        <div class="items-start">
            <h1 class="text-2xl font-semibold leading-7 text-gray-900 mb-2">Dashboard</h1>
            <DashboardFilter :filters="filters" />
            <div class="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                <div class="flex items-center bg-blue-100 text-blue-800 rounded-xl p-4 space-x-4 hover:shadow-lg hover:scale-105 transition cursor-pointer">
                    <div class="rounded-full bg-blue-200 p-3 flex items-center justify-center">
                        <svg class="h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                        </svg>
                    </div>
                    <div class="flex flex-col">
                        <p class="text-2xl font-bold">{{ leadSequenceCount }}</p>
                        <p class="text-sm text-blue-700 opacity-90">Sent Emails</p>
                    </div>
                </div>
                <div class="flex items-center bg-green-100 text-green-800 rounded-xl p-4 space-x-4 hover:shadow-lg hover:scale-105 transition cursor-pointer">
                    <div class="rounded-full bg-green-200 p-3 flex items-center justify-center">
                        <svg class="h-8 w-8 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <div class="flex flex-col">
                        <p class="text-2xl font-bold">{{ deliveredEmails }}</p>
                        <p class="text-sm text-green-700 opacity-90">Delivered</p>
                    </div>
                </div>
                <div class="flex items-center bg-purple-100 text-purple-800 rounded-xl p-4 space-x-4 hover:shadow-lg hover:scale-105 transition cursor-pointer">
                    <div class="rounded-full bg-purple-200 p-3 flex items-center justify-center">
                        <svg class="h-8 w-8 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75A2.25 2.25 0 014.5 4.5h15a2.25 2.25 0 012.25 2.25v10.5A2.25 2.25 0 0119.5 19.5h-15a2.25 2.25 0 01-2.25-2.25V6.75zM4.5 8.25h6.75m-6.75 3h4.5m7.5-3h.008v.008h-.008V8.25zm0 3h.008v.008h-.008V11.25z" />
                        </svg>
                    </div>
                    <div class="flex flex-col">
                        <p class="text-2xl font-bold">{{ leadCount }}</p>
                        <p class="text-sm text-purple-700 opacity-90">Leads</p>
                    </div>
                </div>
                <div class="flex items-center bg-amber-50 text-amber-800 rounded-xl p-4 space-x-4 hover:shadow-lg hover:scale-105 transition cursor-pointer">
                    <div class="rounded-full bg-amber-100 p-3 flex items-center justify-center">
                        <svg class="h-8 w-8 text-amber-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 5h18M6 12h12M9 19h6" />
                        </svg>
                    </div>
                    <div class="flex flex-col">
                        <p class="text-2xl font-bold">{{ sequenceCount }}</p>
                        <p class="text-sm text-amber-700 opacity-90">Sequence</p>
                    </div>
                </div>
            </div>
            <div class="mt-10 flex flex-col lg:flex-row gap-6">
                <div class="bg-white p-6 rounded-2xl shadow w-full lg:w-[35%]">
                  <h2 class="text-2xl font-bold mb-4">Email Performance</h2>
                  <DoughnutChart
                        :labels="['Sent', 'Delivered', 'Opened', 'Clicked']"
                        :values="[metrics.sent, metrics.delivered, metrics.opened, metrics.clicked]"
                        :colors="['#2196F3', '#4CAF40', '#FFC107', '#FF5722']"
                    />
                </div>
                <div class="bg-white p-6 rounded-2xl shadow w-full lg:w-[65%]">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">Email Data Chart</h2>
                    <div class="w-full">
                        <LineChart :chart-data="props.fallbackData" :chart-options="chartOptions" />
                    </div>
                </div>
            </div>
            <!-- <div class="p-6">
                <h2 class="text-2xl font-bold mb-4">Email Performance</h2>
                <DoughnutChart
                    :labels="['Sent', 'Delivered', 'Opened', 'Clicked']"
                    :values="[metrics.sent, metrics.delivered, metrics.opened, metrics.clicked]"
                    :colors="['#4CAF50', '#2196F3', '#FFC107', '#FF5722']"
                />
            </div> -->
        </div>
    </AdminLayout>
</template>

