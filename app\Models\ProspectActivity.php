<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProspectActivity extends Model
{
    use HasFactory;

    protected $fillable = [
        'prospect_id',
        'user_id',
        'activity_type',
        'title',
        'description',
        'metadata',
        'activity_date',
    ];

    protected $casts = [
        'metadata' => 'array',
        'activity_date' => 'datetime',
    ];

    // Relationships
    public function prospect()
    {
        return $this->belongsTo(Prospect::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Accessors
    public function getActivityTypeDisplayAttribute()
    {
        return match($this->activity_type) {
            'email_sent' => 'Email Sent',
            'email_received' => 'Email Received',
            'call_made' => 'Call Made',
            'call_received' => 'Call Received',
            'meeting_scheduled' => 'Meeting Scheduled',
            'meeting_completed' => 'Meeting Completed',
            'note_added' => 'Note Added',
            'status_changed' => 'Status Changed',
            'linkedin_message' => 'LinkedIn Message',
            'proposal_sent' => 'Proposal Sent',
            'follow_up_scheduled' => 'Follow-up Scheduled',
            'document_shared' => 'Document Shared',
            'other' => 'Other',
            default => ucfirst(str_replace('_', ' ', $this->activity_type))
        };
    }

    public function getActivityIconAttribute()
    {
        return match($this->activity_type) {
            'email_sent', 'email_received' => 'mail',
            'call_made', 'call_received' => 'phone',
            'meeting_scheduled', 'meeting_completed' => 'calendar',
            'note_added' => 'edit',
            'status_changed' => 'refresh',
            'linkedin_message' => 'linkedin',
            'proposal_sent' => 'file-text',
            'follow_up_scheduled' => 'clock',
            'document_shared' => 'share',
            default => 'activity'
        };
    }
}
