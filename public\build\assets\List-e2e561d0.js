import{r as c,T as I,x as q,o as a,c as i,a as l,u as S,w as n,F as m,Z as E,b as e,k as g,y as D,g as A,v as b,d as _,e as H,f as Y,n as U,O as Z,t as r}from"./app-d6eb42fc.js";import{_ as G,a as J,b as P}from"./AdminLayout-5cfec363.js";import{_ as K}from"./CreateButton-543227f7.js";import{_ as Q}from"./SecondaryButton-14965b81.js";import{D as R}from"./DangerButton-293f02ed.js";import{M as W}from"./Modal-30c61fff.js";import{_ as X}from"./Pagination-206e2cc2.js";import{_ as p}from"./InputLabel-712384a1.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const ee={class:"animate-top"},te={class:"flex justify-between items-center"},se=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Prospects")],-1),oe={class:"flex justify-end"},le={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},ae={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},re=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),ie={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},ne={class:"flex justify-end"},de={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},ce={class:"flex justify-between items-center mb-2"},ue={class:"flex items-center"},me=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),ge={class:"grid grid-cols-1 md:grid-cols-5 gap-4 mb-6"},pe=e("option",{value:""},"All Statuses",-1),fe=["value"],he=e("option",{value:""},"All Priorities",-1),_e=["value"],xe=e("option",{value:""},"All Sources",-1),ye=["value"],ve=e("option",{value:""},"All Users",-1),we=["value"],be={class:"mt-8 overflow-x-auto sm:rounded-lg"},ke={class:"shadow sm:rounded-lg"},Ce={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Se=e("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[e("tr",{class:"border-b-2"},[e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," Prospect "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," COMPANY "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," Source "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," Status "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," Priority "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," Budget "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," Follow Up "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," ACTION ")])],-1),Ae={key:0},Ue={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},Me={class:"flex items-center"},Be={class:"text-sm font-medium text-gray-900"},je={class:"text-sm text-gray-500"},Le={class:"px-4 py-2.5 min-w-36"},Ve={class:"text-sm text-gray-900"},De={class:"text-sm text-gray-500"},Pe={class:"px-4 py-2.5 min-w-36"},$e={class:"text-sm text-gray-900"},Ne={class:"px-4 py-2.5 min-w-36"},ze={class:"px-4 py-2.5 min-w-36"},Te={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},Fe={class:"px-4 py-2.5 min-w-36"},Oe={class:"items-center px-4 py-2.5"},Ie={class:"flex items-center justify-start gap-4"},qe=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Ee=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),He=e("span",{class:"text-sm text-gray-700 leading-5"}," View ",-1),Ye=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),Ze=e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),Ge=["onClick"],Je=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),Ke=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),Qe=[Je,Ke],Re={key:1},We=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Xe=[We],et={class:"p-6"},tt=e("h2",{class:"text-lg font-medium text-gray-900"}," Delete Prospect ",-1),st=e("p",{class:"mt-1 text-sm text-gray-600"}," Are you sure you want to delete this prospect? This action cannot be undone. ",-1),ot={class:"mt-6 flex justify-end space-x-3"},ft={__name:"List",props:{prospects:Object,filters:Object,filterOptions:Object},setup(d){var M,B,j,L,V;const f=d,u=c(((M=f.filters)==null?void 0:M.search)||""),x=c(((B=f.filters)==null?void 0:B.status)||""),y=c(((j=f.filters)==null?void 0:j.priority)||""),v=c(((L=f.filters)==null?void 0:L.lead_source)||""),w=c(((V=f.filters)==null?void 0:V.assigned_to)||""),k=I({}),h=c(!1),C=c(null);q([u,x,y,v,w],()=>{Z.get(route("prospects.index"),{search:u.value,status:x.value,priority:y.value,lead_source:v.value,assigned_to:w.value},{preserveState:!0,replace:!0})},{debounce:300});const $=o=>{C.value=o,h.value=!0},N=()=>{k.delete(route("prospects.destroy",C.value),{onSuccess:()=>{h.value=!1,C.value=null}})},z=o=>({new:"bg-blue-100 text-blue-800",contacted:"bg-yellow-100 text-yellow-800",qualified:"bg-green-100 text-green-800",unqualified:"bg-red-100 text-red-800",converted:"bg-purple-100 text-purple-800",lost:"bg-gray-100 text-gray-800"})[o]||"bg-gray-100 text-gray-800",T=o=>({low:"bg-gray-100 text-gray-800",medium:"bg-blue-100 text-blue-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800",F=o=>o?new Date(o).toLocaleDateString():"-",O=o=>o?new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(o):"-";return(o,s)=>(a(),i(m,null,[l(S(E),{title:"Prospects"}),l(G,null,{default:n(()=>[e("div",ee,[e("div",te,[se,e("div",oe,[e("div",le,[e("div",ae,[re,g(e("input",{id:"search-field","onUpdate:modelValue":s[0]||(s[0]=t=>u.value=t),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,512),[[D,u.value]])])]),e("div",ie,[e("div",ne,[l(K,{href:o.route("prospects.create")},{default:n(()=>[A(" Add New Prospect ")]),_:1},8,["href"])])])])]),e("div",de,[e("div",ce,[e("div",ue,[me,l(p,{for:"customer_id",value:"Filters",class:"ml-2"})])]),e("div",ge,[e("div",null,[l(p,{for:"search",value:"Search"}),g(e("input",{id:"search","onUpdate:modelValue":s[1]||(s[1]=t=>u.value=t),type:"text",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500",placeholder:"Search prospects..."},null,512),[[D,u.value]])]),e("div",null,[l(p,{for:"status",value:"Status"}),g(e("select",{id:"status","onUpdate:modelValue":s[2]||(s[2]=t=>x.value=t),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[pe,(a(!0),i(m,null,_(d.filterOptions.statuses,t=>(a(),i("option",{key:t,value:t},r(t.charAt(0).toUpperCase()+t.slice(1)),9,fe))),128))],512),[[b,x.value]])]),e("div",null,[l(p,{for:"priority",value:"Priority"}),g(e("select",{id:"priority","onUpdate:modelValue":s[3]||(s[3]=t=>y.value=t),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[he,(a(!0),i(m,null,_(d.filterOptions.priorities,t=>(a(),i("option",{key:t,value:t},r(t.charAt(0).toUpperCase()+t.slice(1)),9,_e))),128))],512),[[b,y.value]])]),e("div",null,[l(p,{for:"lead_source",value:"Lead Source"}),g(e("select",{id:"lead_source","onUpdate:modelValue":s[4]||(s[4]=t=>v.value=t),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[xe,(a(!0),i(m,null,_(d.filterOptions.leadSources,t=>(a(),i("option",{key:t,value:t},r(t.charAt(0).toUpperCase()+t.slice(1).replace("_"," ")),9,ye))),128))],512),[[b,v.value]])]),e("div",null,[l(p,{for:"assigned_to",value:"Assigned To"}),g(e("select",{id:"assigned_to","onUpdate:modelValue":s[5]||(s[5]=t=>w.value=t),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},[ve,(a(!0),i(m,null,_(d.filterOptions.users,t=>(a(),i("option",{key:t.id,value:t.id},r(t.name),9,we))),128))],512),[[b,w.value]])])])]),e("div",be,[e("div",ke,[e("table",Ce,[Se,d.prospects.data.length>0?(a(),i("tbody",Ae,[(a(!0),i(m,null,_(d.prospects.data,t=>(a(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Ue,[e("div",Me,[e("div",null,[e("div",Be,r(t.first_name)+" "+r(t.last_name),1),e("div",je,r(t.email),1)])])]),e("td",Le,[e("div",Ve,r(t.company||"-"),1),e("div",De,r(t.position||"-"),1)]),e("td",Pe,[e("span",$e,r(t.lead_source.charAt(0).toUpperCase()+t.lead_source.slice(1).replace("_"," ")),1)]),e("td",Ne,[e("span",{class:U([z(t.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},r(t.status.charAt(0).toUpperCase()+t.status.slice(1)),3)]),e("td",ze,[e("span",{class:U([T(t.priority),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},r(t.priority.charAt(0).toUpperCase()+t.priority.slice(1)),3)]),e("td",Te,r(O(t.estimated_budget)),1),e("td",Fe,r(F(t.next_follow_up_at)),1),e("td",Oe,[e("div",Ie,[l(J,{align:"right",width:"48"},{trigger:n(()=>[qe]),content:n(()=>[l(P,{href:o.route("prospects.show",t.id)},{svg:n(()=>[Ee]),text:n(()=>[He]),_:2},1032,["href"]),l(P,{href:o.route("prospects.edit",t.id)},{svg:n(()=>[Ye]),text:n(()=>[Ze]),_:2},1032,["href"]),e("button",{type:"button",onClick:lt=>$(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Qe,8,Ge)]),_:2},1024)])])]))),128))])):(a(),i("tbody",Re,Xe))])])]),d.prospects.links.length>0?(a(),H(X,{key:0,class:"mt-6",links:d.prospects.links},null,8,["links"])):Y("",!0)]),l(W,{show:h.value,onClose:s[7]||(s[7]=t=>h.value=!1)},{default:n(()=>[e("div",et,[tt,st,e("div",ot,[l(Q,{onClick:s[6]||(s[6]=t=>h.value=!1)},{default:n(()=>[A(" Cancel ")]),_:1}),l(R,{onClick:N,class:U({"opacity-25":S(k).processing}),disabled:S(k).processing},{default:n(()=>[A(" Delete Prospect ")]),_:1},8,["class","disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{ft as default};
