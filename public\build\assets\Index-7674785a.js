import{o as d,e as h,w as o,b as e,s as f,u as c,j as m,T as _,r as n,q as p,c as g,a as l,F as x,Z as v,k as w,y}from"./app-039877b4.js";import{_ as b}from"./AdminLayout-b2172af9.js";const k={class:"text-lg font-semibold leading-7 text-gray-900"},B={__name:"CustomButton",props:{href:{type:String,required:!0},active:{type:Boolean}},setup(r){return(s,a)=>(d(),h(c(m),{href:r.href,class:"flex justify-between items-center border border-gray-300 px-4 py-6 bg-white rounded-lg shadow-sm hover:shadow hover:border-gray-300"},{default:o(()=>[e("h3",k,[f(s.$slots,"default")])]),_:3},8,["href"]))}},C={class:"animate-top"},M={class:"sm:flex sm:items-center"},S=e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Settings")],-1),V={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},$={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},z=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),j={class:"border-gray-900 mt-10",style:{height:"500px"}},q={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},F={class:"sm:col-span-2"},L=e("svg",{class:"w-12 h-12 fill-current text-blue-600 inline-block",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M12 2l7 4v6c0 5-3 9-7 10-4-1-7-5-7-10V6l7-4z"}),e("circle",{cx:"12",cy:"10",r:"3",fill:"white"}),e("path",{d:"M9 16c1-2 5-2 6 0",stroke:"white","stroke-width":"2",fill:"none"})],-1),N=e("span",{class:"font-semibold text-lg ml-4"},"Roles & Permissions",-1),T={__name:"Index",setup(r){_({});const s=n(""),a=n([{name:"Roles & Permissions",route:"roles.index"}]);return p(()=>a.value.filter(t=>t.name.toLowerCase().includes(s.value.toLowerCase()))),(t,i)=>(d(),g(x,null,[l(c(v),{title:"Settings"}),l(b,null,{default:o(()=>[e("div",C,[e("div",M,[S,e("div",V,[e("div",$,[z,w(e("input",{id:"search-field","onUpdate:modelValue":i[0]||(i[0]=u=>s.value=u),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,512),[[y,s.value]])])])]),e("div",j,[e("div",q,[e("div",F,[l(B,{href:t.route("roles.index")},{default:o(()=>[L,N]),_:1},8,["href"])])])])])]),_:1})],64))}};export{T as default};
